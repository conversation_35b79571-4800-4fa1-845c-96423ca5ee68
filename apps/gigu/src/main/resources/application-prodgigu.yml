tenant:
  id: GIGU
  name: gigu
  displayName: GigU Conta
  domain: giguconta.app
  mail-domain: giguconta.app
  citation: "o GigU Conta"

gigu:
  callback:
    identity: 5c207662-4dce-4700-b002-0a75459b6c37
    secret: 2d9e07ac-57cf-4643-a20c-969f428656e5

friday-callback:
  identity: ${integrations.settlement.username}
  secret: ${integrations.settlement.password}

arbi-callback:
  identity: e6f3f32d-6736-4148-bff4-d4709b86c2ee
  secret: FROM_AWS_SECRETS

credit-card:
  soft-descriptor-prefix: "GigU*"

modules:
  automatic-pix.enabled: true
  manual-entry.enabled: true
  pfm.enabled: true
  chatbot-ai.enabled: true
  event-api.enabled: true
  openfinance.enabled: true
  push-notification.enabled: true
  in-app-subscription-coupon.enabled: false

application:
  region: us-east-1
  accountNumber: ************

email-sender:
  basic-auth:
    username: 2c771069-9b8a-4390-a894-318ca9ba742a
    password: AT_AWS_SECRETS

features:
  sendEmailViaApiFridayAi: false
  createTopic: true
  vehicleDebts: true
  sendEmailReceipt: true
  eventBus:
    enabled: true
    publisher.sns.enabled: true
  inAppSubscription: 0
  asyncSettlement: true
  forwardEmailToManualWorkflow: false
  automaticUserRegister: false
  zeroAuthEnabled: false
  creditCardChallenge: true
  updateScheduleOnAmountLowered: true
  pinCode.enabled: false
  credit-card.provider: "software-express"
  credit-card-risk-analisys.provider: "clearsale"
  imageReceipt: true

disable.export-s3: true

integrations:
  arbi:
    contaTitular: "**********" #` CONTA DDA
    contaLiquidacao: "**********"
    inscricao: **************
    contaCashin: "**********" # DEFINIR CONTA CASHIN
    fepweb:
      formTemplateExtCode: gigu_cc_simples_pf
      productExtCode: gigu_cc_pf
      businessUnitExtCode: "140"
  cognito:
    userPoolId: ${application.region}_CojOrzW7e
    jwtCookie:
      userPoolClientId: 6mnjfvonu9lofa2sacodv5sd8m
  adjust:
    app-token: "65v9kyfk3nr4"
    event-tokens:
      accountActivated:
        token: "yxtqjo"
      onboardingTestPixCreated:
        token: "28rygp"
      trialStarted:
        token: "k68bz7"
      trialConverted:
        token: "14xvx9"
  intercom:
    enabled: true
    tenant: "GigU"
    token: XXX
    webIdVerificationSecret: XXX
    iosIdVerificationSecret: XXX
    androidIdVerificationSecret: XXX
  firebase:
    measurementId: "G-MYD87JFPG2"
  liveness:
    host: "https://liveness.friday.ai"
    user: XXX
    password: XXX
  settlement:
    host: https://liquidacao.friday.ai
    username: 24cbcc7f-4408-4d12-b18d-************
    password: $2y$19$npO3lIz0ToTa.YZ/xWx07eymYxtSw.Y8QigUc9HaT4uJsewvsLLLL
    requestProtocol: HTTP
  chatbotai:
    sendMessageProtocol: HTTP
  openfinance:
    clientid: 48dd52fb-7e66-4ba9-ab35-cbbfcb16cc57
    clientsecret: b51798cf-a6e0-4687-bd20-8ed4faa7601f

kms.openfinance.keyid: TODO

settlementFundsTransfer:
  payerName: Stopclub Tecnologia, Solucoes e Servicos LTDA
  payerDocument: "**************"
  recipientName: Friday Pagamentos Digitais LTDA
  recipientDocument: **************
  description: pagamento de conta
  fraudFundsAccount: 3201012 # precisamos da conta da me poupe. Por enquanto está a da friday
  originSettlementBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 320559
    accountDv: 0
  originCashinBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 320559
    accountDv: 0
    ispb: ********
  recipientBankAccount: # é o fluxo da celcoin. Botamos um valor qualquer para não transferir
    accountType: CHECKING
    routingNo: 1
    accountNo: 0000000
    accountDv: 2
    ispb: ********

communication-centre:
  forward.configuration-set: gigu_failure_rendering_notification_configuration_set
  email:
    bucket-unprocessed-emails: ses-unprocessed-emails-gigu
    configuration-set-name: gigu_failure_rendering_notification_configuration_set
    notification.virus.bucket: quarantine-emails-gigu
  integration:
    blip:
      templates:
        welcomeAccountCreatedWithoutChatbot: welcome_account_created_without_chatbot_gigu__1_0_0
        registerDenied: register_denied_gigu__4_26_0
        walletMemberJoined: wallet_member_joined_gigu__4_17_1
        registerUpdated: register__updated_gigu__4_12_1
        walletBarcodeBillApprovedPaymentNonPayable: wallet_barcode_bill_approved_payment_non_payable_gigu__4_17_1
        insufficientBalanceFirstCashIn: insufficient_balance_first_cash_in_gigu__4_12_0
        fredOnboardingWelcome: gigu_onboarding_welcome_1_0_9
        triPixReminderNextDay: tri_pix_reminder_next_day_gigu__1_0_0
        triPixReminderLastDay: tri_pix_reminder_last_day_gigu__1_0_0
        triPixExpired: tri_pix_expired_gigu_1_0_3
        walletInviteAssistantWithAccount: wallet_invite_assistant_with_account_gigu__4_13_3
        walletInviteReminderAssistantWithAccount: wallet_invite_reminder_assistant_with_account_gigu__4_13_0
        firstBillScheduled: first_bill_scheduled_gigu__4_12_0
        pixNotReceivedFailure: pix_not_received_failure_gigu__4_21_0
        subscriptionInsufficientBalance: subscription_insufficient_balance_gigu__4_17_0
        inAppSubscriptionOverdue: in_app_subscription_overdue_gigu__1_0_0
        subscriptionOverdue: subscription_overdue_gigu__4_18_2
        subscriptionOverdueWarningAccountClosure: subscription_overdue_warning_account_closure_gigu__1_0_0
        subscriptionOverdueDay32: subscription_overdue_day32_gigu__1_00_8
        subscriptionOverdueDay04: subscription_overdue_day04_gigu__1_00_0
        subscriptionOverdueDay02: subscription_overdue_day02_gigu__1_00_2
        subscriptionOverdueDay01: subscription_overdue_day01_gigu__1_00_8
        subscriptionCreated: subscription_created_gigu__4_17_1
        creditCardEnabled: credit_card_enabled_gigu__4_20_0
        utilityAccountInvoiceNotFound: utility_account_invoice_not_found_gigu__1_0_0
        utilityAccountDisconnectLegacyAccount: utility_account_disconnect_legacy_account_gigu__1_0_2
        utilityAccountRequestReconnection: utility_account_request_reconnection_gigu__1_0_1
        walletBillScheduleCanceledDueAmountChanged: wallet_bill_schedule_canceled_due_amount_changed_gigu__4_23_1
        walletCashInInsufficientBalance: wallet_cash_in_insufficient_balance_gigu__4_17_0