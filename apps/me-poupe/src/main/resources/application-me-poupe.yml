tenant:
  id: "ME-<PERSON>OUPE"
  name: me poupe
  displayName: Me Poupe!
  chatbotName: Na_th
  supportEmail: <EMAIL>
  allowedOnboardingAssistantIds:
    - "ACCOUNT-86a23551-c712-4fe8-9dc0-932ca6e955a9"
    - "ACCOUNT-9246a34d-048b-4fef-b069-b288c7ece62a"

deeplink-url: "https://use.mepoupe.app/app/"

micronaut:
  application:
    name: bill-payment-service
  server:
    cors:
      enabled: true
      configurations:
        web:
          allowed-origins-regex: ${ALLOWED_ORIGINS_REGEX}
  security:
    enabled: true
    authentication: cookie
    token:
      jwt:
        enabled: true
        generator:
          access-token-expiration: 720000
        cookie:
          enabled: true
          login-success-target-url: ${LOGIN_SUCCESS_TARGET_URL}
          login-failure-target-url: ${LOGIN_FAILURE_TARGET_URL}
          cookie-same-site: Lax
          cookie-secure: true
          cookie-domain: ${COOKIE_DOMAIN}
        signatures:
          jwks:
            awscognito:
              url: ${JW<PERSON>_COGNITO_ME_POUPE_URL}
          secret:
            generator:
              secret: ${BILL_PAYMENT_SECRET}
              jws-algorithm: HS256
application:
  region: us-east-1
  accountNumber:

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: true
  event-api:
    enabled: true
  push-notification:
    enabled: true
  in-app-subscription-coupon:
    enabled: true
  investment-goals:
    enabled: true
  openfinance:
    enabled: true

chatbotAI-auth:
  secret: bKzAm82UAVjUmKUBUpjKu5xUrjml5upVfazJeACYz4P4T2hPNyezrs3BTRmN

integrations:
  intercom:
    tenant: "Na_th"
  liveness:
    user: "LIVENESS_CLIENT_ID-418814a0-5db3-4f9e-b2f6-b6377018fb5e"
    password: "Liveness_gateway_mp_pwd_prod_!@#_to_be_replaced"
  chatbotai:
    host: "https://chatbot.mepoupe.app"
    clientid: "CHATBOT_AI_TRANSACTION_CLIENT_ID-d502c3c4-0b1b-401f-aa75-e86d9f94cd95"
    secret: "5-H1f]QvL-_:uU1:Mk)J]v,s}q4=#UT=GJnoH%p4PnJX-nvjP0HFFPCBBPxA"
  adjust:
    appToken: "wmrz8dvi8jr4"
    eventTokens:
      accountActivated:
        token: "ez141n"
      onboardingTestPixCreated:
        token: "4ibbh7"
      trialStarted:
        token: "e0q5ua"
      trialConverted:
        token: "brl7n8"
  arbi:
    inscricao: ${CNPJ_FRIDAY_ME_POUPE}
    contaTitular: "0" #Conta guarda chuva para os DDAs
    contaLiquidacao: "0"
    contaCashin: "0"
    fepweb:
      formTemplateExtCode: mepoupe_cc_simples_pf
      productExtCode: mepoupe_cc_pf
      businessUnitExtCode: "140"
    paymentTimeLimit: "21:00"
  me-poupe-api:
    client-id: ${ME_POUPE_API_CLIENT_ID}
    client-secret: ${ME_POUPE_API_CLIENT_SECRET}
    user-id-header: "X-User-Id"
  revenueCat:
    host: "https://api.revenuecat.com"
    projectId: "076865ee"
    secretKey: FROM_AWS_SECRETS
    secretKeyV1: FROM_AWS_SECRETS
  fluency:
    callback:
      identity: "STG_FLUENCY_CALLBACK_ID"
      secret: "STG_FLUENCY_CALLBACK_SECRET"
  voomp:
    callback:
      identity: "STG_VOOMP_CALLBACK_ID"
      secret: "STG_VOOMP_CALLBACK_SECRET"
  html2Image:
    lambdaName: "mepoupe-html-to-image"
  manual-workflow:
    emails: <EMAIL>

token:
  onboarding:
    tokenSize: 6
    phoneVerificationDuration: 55
    emailVerificationDuration: 120
    message: "Seu codigo Me Poupe!: %1$s. Nao compartilhe com ninguem.

              @mepoupe.app #%1$s"
    maxErrorCount: 3

accountRegister:
  user_files:
    bucket: ${application.accountNumber}-me-poupe-bill-payment-user-documents

templates-html-to-image:
  instagrammableInvestmentReceipt: "templates/instagrammable-investment-receipt.me-poupe"
  instagrammableExtraInvestmentReceipt: "templates/instagrammable-extra-investment-receipt.me-poupe"
  instagrammableGoalCompletedReceipt: "templates/instagrammable-goal-completed-receipt.me-poupe"
  instagrammableGoalEndDateReachedReceipt: "templates/instagrammable-goal-end-date-reached-receipt.me-poupe"

communication-centre:
  landingPageUrl: https://www.mepoupe.app
  email:
    display-name: ${tenant.displayName}
    return-path: <EMAIL>
    bucket-unprocessed-emails: ses-unprocessed-emails-me-poupe-contas
    configuration-set-name: failure_rendering_notification_configuration_set
    receipt:
      email: <EMAIL>
      display-name: ${tenant.displayName}
    notification:
      email: <EMAIL>
      display-name: ${tenant.displayName}
    virus:
      bucket: quarantine-emails
    templates:
      local:
        emailVerificationTokenPath: "templates/email-verification-token.me-poupe"
        kycDossierFormPath: "templates/kyc/kyc-dossier.me-poupe"
        declarationOfResidencyFormPath: "templates/register/declaration-of-residency.me-poupe"
        accountStatementReportPath: "templates/statement-report.me-poupe"
        walletSummaryReportPath: "templates/wallet-summary-report.me-poupe"
        emailPasswordRecoveryTokenPath: "templates/password-recovery-token.me-poupe"
        statementPdf: "templates/statement-pdf.me-poupe"
        pixReceipt: "templates/bill-receipt-attachment.me-poupe"
        investmentReceipt: "templates/investment-receipt.me-poupe"
        invoiceReceipt: "templates/bill-receipt-attachment.me-poupe"
        barcodeBillReceipt: "templates/bill-receipt-attachment.me-poupe"
        mailReceipt: "templates/mail-receipt.me-poupe"
        inform: "templates/inform.me-poupe"
        investmentRedemptionFinished: "templates/mail-receipt-investment-redemption-finished.me-poupe"
        walletInviteAssistantWithoutAccount: "templates/wallet-invite-assistant-without-account.me-poupe"
        walletInviteCollaboratorWithoutAccount: "templates/wallet-invite-collaborator-without-account.me-poupe"
        walletInviteReminderAssistantWithoutAccount: "templates/wallet-invite-reminder-assistant-without-account.me-poupe"
        walletInviteReminderCollaboratorWithoutAccount: "templates/wallet-invite-reminder-collaborator-without-account.me-poupe"
      ses:
        walletInviteAssistantWithoutAccount: wallet_invite_assistant_without_account__4_13_0
        walletInviteCollaboratorWithoutAccount: wallet_invite_collaborator_without_account__4_13_0
        walletInviteReminderAssistantWithoutAccount: wallet_invite_reminder_assistant_without_account__4_13_0
        walletInviteReminderCollaboratorWithoutAccount: wallet_invite_reminder_collaborator_without_account__4_13_0
  forward:
    configuration-set: failure_rendering_notification_configuration_set
    sender: <EMAIL>
    htmlTemplatePath: "templates/forwarded-email-html.me-poupe.hbs"
    textTemplatePath: "templates/forwarded-email-plain-text.me-poupe.hbs"
    organizationName: Nath
  integration:
    blip:
      templates:
        #1 - chatbot_ai_notify_bills_coming_due_singular_mp__1_0_0 - chatbot-ai
        #5 - chatbot_ai_notify_bills_coming_due_last_warn_mp__1_0_0 - chatbot-ai
        #6 - chatbot_ai_notify_bills_coming_due_mp__1_0_0 - chatbot-ai
        walletPixBillReceipt: wallet_pix_bill_receipt_mp__1_0_0
        walletInvestmentBillReceiptWithBadge: wallet_investment_bill_receipt_with_badge_mp__1_0_2
        walletBillReceiptImage: wallet_bill_receipt_image_mp__3_1_1
        walletBarcodeBillReceipt: wallet_barcode_bill_receipt_mp__1_0_0
        subscriptionOverdueDay01: subscription_overdue_day01_mp__1_0_0
        subscriptionOverdueWarningAccountClosure: subscription_overdue_warning_account_closure_mp__1_0_0
        walletFounderSelfCashInSufficientBalance: wallet_cash_in_sufficient_balance__short_mp_1
        inAppSubscriptionOverdue: in_app_subscription_overdue_mp__1_0_0
        subscriptionOverdue: subscription_overdue_mp__1_0_0
        pixSubscriptionOverdueNotificationChannelDowngradeWarning: subscription_overdue_whatsapp_blocked_pix_2_0_0
        inAppSubscriptionOverdueNotificationChannelDowngradeWarning: subscription_overdue_whatsapp_blocked_3_0_0
        postalBoxAddManualReview: postal_box_add_manual_review_mp__1_0_0
        #18 - welcome_account_created_with_chatbot_mp__1_0_0 - chatbot-ai
        subscriptionCreated: subscription_created_mp_2_0_1
        subscriptionGrantedByInvestment: subscription_granted_2_1_1
        subscriptionOverdueCloseAccount: subscription_overdue_close_account_mp__1_0_0
        registerToken: register_token_mp__1_0_0
        triPixExpired: tri_pix_expired_mp__1_0_0
        triPixReminderNextDay: tri_pix_reminder_next_day_mp__1_0_1
        walletApprovedPaymentCancelled: wallet_approved_payment_cancelled_mp__1_0_0
        #28 - chatbot_ai_notify_bills_coming_due_simple_mp__1_0_0 - chatbot-ai
        reminderNotification: reminder_notification_mp__1_0_0
        firstBillScheduled: first_bill_scheduled_mp__1_0_0
        triPixReminderLastDay: tri_pix_reminder_last_day_mp__1_0_0 #31 - ESTA VERMELHO NA PLANILHA
        #32 - chatbot_outdated_action_mp__1_0_0 - chatbot-ai
        subscriptionOverdueDay02: subscription_overdue_day02_mp__1_0_1
        utilityAccountUpdateStatus: utility_account_update_status_mp__1_0_0
        walletPostalBoxAddDuplicate: wallet_postal_box_add_duplicate_mp__1_0_0
        signUpBasicUpdateDataNeeded: sign_up_basic_update_data_needed_mp__1_0_0
        registerUpdated: register__updated_mp__1_0_0
        #39 - connected_flow__account_mp__1_0_0  - NOT FOUND

        #daqui para baixo ainda estava branco na planilha. Tem que conferir
        utilityAccountInvoiceNotFound: utility_account_invoice_not_found_mp__1_0_0
        walletBillScheduleCanceledDueAmountHigherThanDailyLimit: wallet_bill_schedule_canceled_due_amount_higher_than_daily_limit_mp__1_0_0
        #43 - bonus_setup_mp__1_0_0 - NOT FOUND
        #44 - bonus_zero_feature_2_msg_mp__1_0_0 - NOT FOUND
        walletCashInInsufficientBalance: wallet_cash_in_insufficient_balance_mp__1_0_0
        reminderExpiredNotificationSingular: reminder_expired_notification_singular_mp__1_0_0
        walletBarcodeBillApprovedPaymentNonPayable: wallet_barcode_bill_approved_payment_non_payable_mp__1_0_0
        walletInvoiceBillReceipt: wallet_invoice_bill_receipt_mp__1_0_0
        #49 - welcome_password_mp__1_0_0 -> welcome_account_created_with_chatbot__1_0_0 - chatbot-ai
        #50 - reminder_notification_response_success_mp__1_0_0 - chatbot-ai
        #51 - welcome_tri_pix_mp__1_0_0 -> tri_pix_created__1_0_0 - chatbot-ai
        utilityAccountUpdateStatusWithDetails: utility_account_update_status_with_details_mp__1_0_0
        #53 - tri_pix_reminder_mp__1_0_0 - NOT FOUND
        registerUpgraded: register_upgraded_mp__1_0_0
        registerDenied: register_denied_mp__1_0_0
        billComingDueSecondaryWallet: bill_coming_due_secondary_wallet_mp__1_0_0
        barcodeBillCloseOverdueKnownAuthorNoDescription: barcode_bill_close_overdue_known_author_no_description_mp__1_0_0
        walletLastAlertPaymentOverdueToday: wallet_last_alert_payment_overdue_today_mp__1_0_0
        upgradeCompleted: upgrade_completed_mp__1_0_0
        walletOnePixPay: wallet_one_pix_pay_mp__1_0_0
        walletInvoicePaymentFailure: wallet_invoice_payment_failure_mp__1_0_0
        reminderExpiredNotification: reminder_expired_notification_mp__1_0_0
        #63 - chatbot_ai_open_my_bank_app_mp__1_0_0 - chatbot-ai
        walletInviteAssistantWithAccount: wallet_invite_assistant_with_account_mp__1_0_1
        subscriptionOverdueDay04: subscription_overdue_day04_mp__1_0_2
        #66 - chatbot_ai_pix_confirmation_mp__1_0_0 - chatbot-ai
        walletBillScheduleCanceledDueAmountChanged: wallet_bill_schedule_canceled_due_amount_changed_mp__1_0_0
        #68 - chatbot_sufficient_balance_mp__1_0_0 - chatbot-ai
        itpTransactionFailed: itp_transaction_failed_mp__1_0_0
        subscriptionOverdueDay32: subscription_overdue_day32_mp__1_0_0
        utilityAccountInvoiceScanError: utility_account_invoice_scan_error_mp__1_0_0
        walletBillSchedulePostponedDueLimitReached: wallet_bill_schedule_postponed_due_limit_reached_mp__1_0_0
        walletPostalBoxAddNonPayable: wallet_postal_box_add_non_payable_mp__1_0_0
        barcodeBillCloseOverdueKnownAuthorNoDescriptionWithHint: barcode_bill_close_overdue_known_author_no_description_with_hint_mp__1_0_0
        barcodeBillCloseOverdueKnownAuthorWithDescription: barcode_bill_close_overdue_known_author_with_description_mp__1_0_0
        barcodeBillCloseOverdueNoAuthorNoDescription: barcode_bill_close_overdue_no_author_no_description_mp__1_0_0
        barcodeBillCloseOverdueNoAuthorWithDescription: barcode_bill_close_overdue_no_author_with_description_mp__1_0_0
        connectedFlowUtilityAccount: connected_flow_utility_account_mp__1_0_0
        connectedFlowUtilityAccountWithBills: connected_flow_utility_account_with_bills_mp__1_0_0
        connectedUtilityAccount: connected_utility_account_mp__1_0_0
        creditCardEnabled: credit_card_enabled_mp__1_0_0
        disconnectedScrapingUtilityAccount: disconnected_scraping_utility_account_mp__1_0_0
        fredOnboardingWelcome: fred_onboarding_welcome_mp__1_0_0
        insufficientBalanceFirstCashIn: insufficient_balance_first_cash_in_mp__1_0_0
        invoiceCloseOverdueNoDescription: invoice_close_overdue_no_description_mp__1_0_0
        invoiceCloseOverdueWithDescription: invoice_close_overdue_with_description_mp__1_0_0
        mailboxBillInsecure: mailbox_bill_insecure_mp__1_0_0
        pixNotReceivedFailure: pix_not_received_failure_mp__1_0_0
        postalBoxAddNotVisibleBillAlreadyExists: postal_box_add_not_visible_bill_already_exists_mp__1_0_0
        subscriptionInsufficientBalance: subscription_insufficient_balance_mp__1_0_0
        userAuthenticationRequired: user_authentication_required_mp__1_0_0
        utilityAccountDisconnectLegacyAccount: utility_account_disconnect_legacy_account_mp__1_0_0
        utilityAccountRequestReconnection: utility_account_request_reconnection_mp__1_0_0
        walletBillScheduleCanceledDueCreditCardDenied: wallet_bill_schedule_canceled_due_credit_card_denied_mp__1_0_0
        walletCashInWithCreditCardFailed: wallet_cash_in_with_credit_card_failed_mp__1_0_0
        walletInsufficientBalance: wallet_insufficient_balance_mp__1_0_0
        walletInsufficientBalanceAfterHours: wallet_insufficient_balance_after_hours_mp__1_0_0
        walletInsufficientBalanceSecondaryWallet: wallet_insufficient_balance_secondary_wallet_mp__1_0_0
        walletInviteReminderAssistantWithAccount: wallet_invite_reminder_assistant_with_account_mp__1_0_0
        walletInvoicePaymentReturned: wallet_invoice_payment_returned_mp__1_0_0
        walletLastAlertPaymentOverdueTodayWithHint: wallet_last_alert_payment_overdue_today_with_hint_mp__1_0_0
        walletMemberJoined: wallet_member_joined_mp__1_0_0
        walletOnePixPayFailure: wallet_one_pix_pay_failure_mp__1_0_0
        walletOnePixPaySingular: wallet_one_pix_pay_singular_mp__1_0_0
        walletPostalBoxAddNonPayableNoData: wallet_postal_box_add_non_payable_no_data_mp__1_0_0
        walletPostalBoxAddNotAuthorized: wallet_postal_box_add_not_authorized_mp__1_0_0
        walletPostalBoxAddPaidExternally: wallet_postal_box_add_paid_externally_mp__1_0_0
        walletPostalBoxAddPaidExternallyWithoutData: wallet_postal_box_add_paid_externally_without_data_mp__1_0_0
        walletPostalBoxAddValidationFailure: wallet_postal_box_add_validation_failure_mp__1_0_0
        welcomeAccountCreatedWithoutChatbot: welcome_account_created_without_chatbot_mp__1_0_0
        investmentRedemptionCreated: investment_redemption_created_mp__1_0_0
        investmentRedemptionCompleted: investment_redemption_completed_mp__1_0_10
        investmentRedemptionFailed: investment_redemption_failed_mp__1_0_0
        investmentRedemptionPartialFailed: investment_redemption_partial_failed_mp__1_0_0
        sweepingAccountConnected: open_finance_connection_success__mp_1_0_0
        sweepingAccountConnectionFailed: open_finance_connection_failed_generic__mp_1_0_0
        sweepingAccountEdit: open_finance_connection_edit_success__mp_1_0_0
        sweepingAccountEditFailed: open_finance_connection_edit_failed__mp_1_0_4
        sweepingCashInError: sweeping_transfer_error_mp__1_0_0
        sweepingCashInInsufficientBalanceError: sweeping_transfer_balance_error_mp_1_1_0
        goalWithDailyLiquidityCompletedByDate: goal_with_daily_liquidity_completed_by_date__mp_1_0_0
        goalWithDailyLiquidityCompletedByAmount: goal_with_daily_liquidity_completed_by_amount__mp_1_0_0
        goalWithMaturityLiquidityCompletedByDate: goal_with_maturity_liquidity_completed_by_date__mp_1_0_0
        goalWithMaturityLiquidityCompletedByAmount: goal_with_maturity_liquidity_completed_by_amount__mp_1_0_0

        goalWithDailyLiquidityCompletedByDateWithBadge: goal_with_daily_liquidity_completed_by_date_with_badge__mp_1_0_0
        goalWithDailyLiquidityCompletedByAmountWithBadge: goal_with_daily_liquidity_completed_by_amount_with_badge__mp_1_0_0
        goalWithMaturityLiquidityCompletedByDateWithBadge: goal_with_maturity_liquidity_completed_by_date_with_badge__mp_1_0_0
        goalWithMaturityLiquidityCompletedByAmountWithBadge: goal_with_maturity_liquidity_completed_by_amount_with_badge__mp_1_0_0

        goalWithDailyLiquidityPausedByDate: goal_with_daily_liquidity_paused_by_date__mp_1_0_0
        goalWithDailyLiquidityPausedByAmount: goal_with_daily_liquidity_paused_by_amount__mp_1_0_0


emailDomain: "mepoupe.app"

sqs:
  settlementRequestQueueName: settlement-me-poupe-request
  settlementResponseQueueName: settlement-me-poupe-response

CNPJ_FRIDAY_ME_POUPE: **************


urls:
  site: https://use.mepoupe.app
  api: https://api.mepoupe.app
  termsOfUse: "https://me-poupe-contas-bill-payment-public-production.s3.amazonaws.com/current/Me_Poupe_Termos_de_Uso.pdf"

internalBankService:
  omnibusBankAccount:
    accountPaymentMethodId: BANK-ACCOUNT-OMNIBUS
    document: ${CNPJ_FRIDAY_ME_POUPE}
    name: FRIDAY INSTITUICAO DE PAGAMENTOS LTDA
    bankNo: 213
    routingNo: 1
    accountNo: 0 # não usamos conta bolsão pois é usada apenas para contas virtuais e não temos

revenue-cat-callback:
  identity: 0a3e66e9-20d3-4811-9563-75979c7ce0eb
  secret: AT_AWS_SECRETS

receipt:
  bucketName: me-poupe-contas-bill-receipts

email-notification:
  emailVerificationTokenSubject: "Me Poupe! - Verificação de email"
  emailPasswordRecoveryTokenSubject: "Me Poupe! - Recuperação de Senha"

NOTIFICATION_EMAIL: "<EMAIL>"
NEW_ACCOUNT_NON_SENSITIVE_RECIPIENTS: "<EMAIL>"
NEW_ACCOUNT_SENSITIVE_RECIPIENTS: "<EMAIL>"

email:
  notification:
    email: ${NOTIFICATION_EMAIL}
    display-name: ${tenant.displayName}
  finance-report-recipients: <EMAIL>
  newAccountReport:
    recipients: "<EMAIL>"
  newUpgradeAccount:
    recipients: ${NEW_ACCOUNT_NON_SENSITIVE_RECIPIENTS}
    sensitiveRecipients: ${NEW_ACCOUNT_SENSITIVE_RECIPIENTS}
  newAccount:
    pendingInternalApprove:
      recipients: ${NEW_ACCOUNT_NON_SENSITIVE_RECIPIENTS}
      sensitiveRecipients: ${NEW_ACCOUNT_SENSITIVE_RECIPIENTS}
    pendingInternalReview:
      recipients: ${NEW_ACCOUNT_NON_SENSITIVE_RECIPIENTS}
      sensitiveRecipients: ${NEW_ACCOUNT_SENSITIVE_RECIPIENTS}
    pendingActivation:
      recipients: ${NEW_ACCOUNT_NON_SENSITIVE_RECIPIENTS}
      sensitiveRecipients: ${NEW_ACCOUNT_SENSITIVE_RECIPIENTS}

notificationHints:
  billCreated:
    - "Te lembro de pagar as contas pelo WhatsApp e ainda faço seus pagamentos de um jeito automático. Experimente!"
    - "Perdendo tempo pagando conta? Com o app Me Poupe! você paga todas as suas contas com apenas 3 cliques. Experimente e economize tempo."
    - "Com minha ajuda você nunca mais vai esquecer de pagar seu personal trainer, diarista ou terapeuta. Agende transferências recorrentes no app que envio os avisos de vencimento e comprovantes por aqui."
  billComingDue:
    - "Tenha total controle e nunca mais esqueça de fazer pagamentos importantes. Com minha ajuda você agenda pagamentos recorrentes e pode suspendê-los quando quiser."
    - "Já sonhou em não precisar mais se preocupar com qual conta vence em qual dia? Com a Me Poupe! você resolve todas de uma só vez. Experimente!"
    - "Está gostando de receber os lembretes das suas contas a pagar pelo WhatsApp? Imagina se elas forem pagas de jeito automático? Acesse o app e experimente!"
  billComingDueLastWarn:
    - "Agende transferências recorrentes no app Me Poupe! e que te envio lembretes de pagamento por aqui. Experimente!"
    - "Com a Me Poupe! você tem a praticidade de pagar vários tipos diferentes de contas de uma vez só. Não é magia, é tecnologia!"
    - "Tem gente que gosta de receber meus avisos por aqui para lembrar das contas a pagar. Mas se quiser simplificar ainda mais sua vida, você pode agendar e pagar tudo pelo app Me Poupe! de uma vez só."
  billOverdueYesterday:
    - "Agende transferências recorrentes no app Me Poupe! que te envio lembretes de pagamento por aqui. Experimente!"
    - "Ainda dá tempo de fazer esse pagamento com a Me Poupe! Aproveite também para agendar pagamentos futuros e organizar sua vida financeira!"
    - "Deixou a conta vencer mesmo com meu lembrete? Agende seus pagamentos no app e não pague mais multa. Experimente!"

investments:
  start-at: "08:00"
  end-at: "16:30"
  maxRedemptionDays: 3655

jobs:
  SynchronizeOmnibusBankAccountJob:
    enabled: false

kms:
  openfinance:
    keyid: 1bed6ba4-02f9-4b82-a029-d2629219a2c1

features:
  imageReceipt: true

account-group-selector:
  register:
    enabled: true
    begin-date: 2025-05-09
    groups:
      - "MAY_2025_CAMPAIGN"