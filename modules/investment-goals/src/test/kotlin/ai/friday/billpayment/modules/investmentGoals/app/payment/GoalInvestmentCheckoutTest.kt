package ai.friday.billpayment.modules.investmentGoals.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.RetryForeverTransactionException
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementRetry
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.payment.transaction.RetryPolicyConfiguration
import ai.friday.billpayment.app.payment.transaction.RetryTransaction
import ai.friday.billpayment.app.payment.transaction.RollbackTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionProcessingException
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAddedInvestment
import ai.friday.billpayment.billScheduledInvestment
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.MonthlyInstallments
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRequestResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePositionId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProduct
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerWithInvestmentResultException
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRisk
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductType
import ai.friday.billpayment.modules.investmentGoals.dailyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.goalAccountId
import ai.friday.billpayment.modules.investmentGoals.goalWalletId
import ai.friday.billpayment.modules.investmentGoals.monthlyGoal
import ai.friday.billpayment.modules.investmentGoals.savingsProduct
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class GoalInvestmentCheckoutTest {

    private val investment = Bill.build(billAddedInvestment, billScheduledInvestment)

    private val goalService = mockk<GoalService>()
    private val investmentManagerService = mockk<InvestmentManagerService>()
    private val goalProductService = mockk<GoalProductService>()
    private val transactionService = mockk<TransactionService>()
    private val goalInvestmentRepository = mockk<GoalInvestmentRepository>() {
        every {
            save(any())
        } just Runs
        every {
            findByBillId(any())
        } returns GoalInvestment(
            id = GoalInvestmentId("ID"),
            goalId = investment.goalId!!,
            billId = investment.billId,
            amount = investment.amountTotal,
            dueDate = investment.dueDate,
            extraInstallment = false,
            paidAt = null,
            status = GoalInvestmentStatus.CREATED,
        )
    }

    private val checkout = GoalInvestmentCheckout(
        waitTime = 1,
        poolingInterval = 1,
        goalService = goalService,
        investmentManagerService = investmentManagerService,
        goalProductService = goalProductService,
        transactionService = transactionService,
        goalInvestmentRepository = goalInvestmentRepository,
        maxRedemptionDays = 3655,
    )

    private val transaction = Transaction(
        type = TransactionType.GOAL_INVESTMENT,
        payer = ACCOUNT.toPayer(),
        paymentData = createSinglePaymentDataWithBalance(
            balance,
            investment.amountTotal,
            payment = null,
        ),
        settlementData = SettlementData(settlementTarget = investment, serviceAmountTax = 0, totalAmount = investment.amountTotal),
        nsu = 0,
        actionSource = ActionSource.System,
        walletId = investment.walletId,
    )

    private val fixedIncomeProduct = FixedIncomeProduct(
        provider = FixedIncomeProvider.ARBI,
        productId = "PRODUTO",
        name = "NOME_DO_PRODUTO",
        index = FixedIncomeIndex.CDI,
        indexPercentage = FixedIncomeIndexRate(100),
        minTransactionValue = 20_00,
        maxTransactionValue = 2000_00,
        tradeTimeLimit = getLocalTime(),
    )

    @Nested
    @DisplayName("execute")
    inner class ExecuteTransaction {

        @Test
        fun `deve falhar quando o goalId não existir`() {
            val result = checkout.execute(
                transaction = transaction.copy(
                    settlementData = transaction.settlementData.copy(
                        settlementTarget = investment.apply {
                            goalId = null
                        },
                    ),
                ),
            )

            result.status shouldBe TransactionStatus.FAILED
            result.settlementData.settlementOperation shouldBe null
            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.FAILED
            }
        }

        @Test
        fun `deve falhar quando o goal não for encontrado`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns GoalNotFoundException(investment.goalId!!).left()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.FAILED
            result.settlementData.settlementOperation shouldBe null
            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.FAILED
            }
        }

        @Test
        fun `deve falhar quando o produto não for encontrado`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns monthlyGoal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns IllegalStateException().left()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.FAILED
            result.settlementData.settlementOperation shouldBe null
            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.FAILED
            }
        }

        @Test
        fun `deve falhar quando o produto for SAVINGS`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns monthlyGoal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns savingsProduct.right()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.FAILED
            result.settlementData.settlementOperation shouldBe null
            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.FAILED
            }
        }

        @Test
        fun `deve falhar quando o investimento falhar e salvar os dados da chamada`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns monthlyGoal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns dailyCDIProduct.right()

            every {
                investmentManagerService.invest(any(), any(), any(), any(), any(), any(), any())
            } returns InvestmentManagerWithInvestmentResultException(
                result = FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.FAILED,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    maturityDate = null,
                    positionId = null,
                ),
                throwable = IllegalStateException("FAKE"),
            ).left()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.FAILED
            result.settlementData.settlementOperation shouldBe null
            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.FAILED
            }
            verify {
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.FAILED
                    },
                )
            }
        }

        @Test
        fun `deve deixar UNKNOWN quando o investimento estiver como CREATED ou REQUESTED e chamar sem data de vencimento quando for diario salvar os dados da chamada`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns monthlyGoal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns dailyCDIProduct.right()

            every {
                investmentManagerService.invest(any(), any(), any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.CREATED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            every {
                transactionService.save(any())
            } just Runs

            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.REQUESTED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.PROCESSING
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalInvestmentSettlementOperation>()

            settlementOperation.investmentRequestId shouldBe InvestmentManagerExternalId("123")
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.REQUESTED
            settlementOperation.amount shouldBe investment.amountTotal
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.product shouldBe null

            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.REQUESTED
            }

            verify {
                investmentManagerService.invest(
                    any(),
                    any(),
                    any(),
                    any(),
                    index = withArg {
                        it shouldBe FixedIncomeIndex.CDI
                    },
                    indexRate = withArg {
                        it shouldBe dailyCDIProduct.indexIncome
                    },
                    forceRedemptionDate = null,
                )

                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.PROCESSING
                    },
                )
            }
        }

        @Test
        fun `deve deixar UNKNOWN quando o investimento estiver como CREATED ou REQUESTED e chamar com maior data possivel quando goal for maior que data limite`() {
            val endDate = LocalDate.now().plusYears(15)
            val goal = Goal(
                id = GoalId(),
                accountId = goalAccountId,
                walletId = goalWalletId,
                categoryId = GoalCategoryId(),
                name = "monthly goal",
                imageUrl = "monthly image url",
                endDate = endDate,
                amount = 10_000_00,
                liquidity = GoalProductLiquidity.MATURITY,
                installments = MonthlyInstallments(
                    amount = 927,
                    dayOfMonth = 1,
                ),
                productId = enabledMonthlyCDIProduct.id,
                installmentInitialAmount = 927,
                status = GoalStatus.ACTIVE,
                lastKnownNetBalanceAmount = 0,
                lastKnownNetBalanceAt = getZonedDateTime(),
            )

            val goalProduct = GoalProduct(
                type = GoalProductType.CDB,
                index = FixedIncomeIndex.CDI,
                indexIncome = FixedIncomeIndexRate(105),
                liquidity = GoalProductLiquidity.MATURITY,
                minimumTermDays = 1815,
                maximumTermDays = 3655,
                asset = "CDB Arbi",
                risk = GoalProductRisk.LOW,
                issuer = "Arbi",
                provider = "Arbi",
                enabled = false,
            )

            every {
                goalService.findGoal(investment.goalId!!)
            } returns goal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns goalProduct.right()

            every {
                investmentManagerService.invest(any(), any(), any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.CREATED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = LocalDate.now().plusDays(3655),
                positionId = null,
            ).right()

            every {
                transactionService.save(any())
            } just Runs

            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.REQUESTED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = LocalDate.now().plusDays(3655),
                positionId = null,
            ).right()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.PROCESSING
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalInvestmentSettlementOperation>()

            settlementOperation.investmentRequestId shouldBe InvestmentManagerExternalId("123")
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.REQUESTED
            settlementOperation.amount shouldBe investment.amountTotal
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.product shouldBe null

            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.REQUESTED
            }

            verify {
                investmentManagerService.invest(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    forceRedemptionDate = withArg {
                        it shouldBe LocalDate.now().plusDays(3655)
                    },
                )
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.PROCESSING
                    },
                )
            }
        }

        @Test
        fun `deve deixar UNKNOWN quando o investimento estiver como CREATED ou REQUESTED e chamar com data de vencimento da meta quando for vencimento e salvar os dados da chamada`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns monthlyGoal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns enabledMonthlyCDIProduct.right()

            every {
                investmentManagerService.invest(any(), any(), any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.CREATED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            every {
                transactionService.save(any())
            } just Runs

            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.REQUESTED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.PROCESSING
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalInvestmentSettlementOperation>()

            settlementOperation.investmentRequestId shouldBe InvestmentManagerExternalId("123")
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.REQUESTED
            settlementOperation.amount shouldBe investment.amountTotal
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.product shouldBe null

            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.REQUESTED
            }

            verify {
                investmentManagerService.invest(
                    any(),
                    any(),
                    any(),
                    any(),
                    index = withArg {
                        it shouldBe FixedIncomeIndex.CDI
                    },
                    indexRate = withArg {
                        it shouldBe enabledMonthlyCDIProduct.indexIncome
                    },
                    forceRedemptionDate = withArg {
                        it shouldBe monthlyGoal.endDate
                    },
                )
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.PROCESSING
                    },
                )
            }
        }

        @Test
        fun `deve deixar COMPLETAR quando o investimento estiver como COMPLETED e salvar os dados da chamada e também da liquidação`() {
            every {
                goalService.findGoal(investment.goalId!!)
            } returns monthlyGoal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns dailyCDIProduct.right()

            every {
                investmentManagerService.invest(any(), any(), any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.CREATED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            every {
                transactionService.save(any())
            } just Runs

            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.COMPLETED,
                fixedIncomeProduct = fixedIncomeProduct,
                errorMessage = null,
                maturityDate = getLocalDate().plusYears(1),
                positionId = FixedIncomePositionId("POSITION_1"),
            ).right()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.COMPLETED
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalInvestmentSettlementOperation>()

            settlementOperation.investmentRequestId shouldBe InvestmentManagerExternalId("123")
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.COMPLETED
            settlementOperation.amount shouldBe investment.amountTotal
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.product shouldBe fixedIncomeProduct

            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.COMPLETED
            }
            verify {
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.DONE
                        it.paidAt.shouldNotBeNull()
                    },
                )
            }
        }

        @Test
        fun `deve colocar a data correta se o investimento for maior que 3655 dias`() {
            val goal = Goal(
                id = GoalId(),
                accountId = goalAccountId,
                walletId = goalWalletId,
                categoryId = GoalCategoryId(),
                name = "monthly goal",
                imageUrl = "monthly image url",
                endDate = LocalDate.now().plusYears(15),
                amount = 10_000_00,
                liquidity = GoalProductLiquidity.MATURITY,
                installments = MonthlyInstallments(
                    amount = 927,
                    dayOfMonth = 1,
                ),
                productId = enabledMonthlyCDIProduct.id,
                installmentInitialAmount = 927,
                status = GoalStatus.ACTIVE,
                lastKnownNetBalanceAmount = 0,
                lastKnownNetBalanceAt = getZonedDateTime(),
            )

            val goalProduct = GoalProduct(
                type = GoalProductType.CDB,
                index = FixedIncomeIndex.CDI,
                indexIncome = FixedIncomeIndexRate(105),
                liquidity = GoalProductLiquidity.MATURITY,
                minimumTermDays = 1815,
                maximumTermDays = 3655,
                asset = "CDB Arbi",
                risk = GoalProductRisk.LOW,
                issuer = "Arbi",
                provider = "Arbi",
                enabled = false,
            )

            every {
                goalService.findGoal(investment.goalId!!)
            } returns goal.right()

            every {
                goalProductService.findProduct(monthlyGoal.productId)
            } returns goalProduct.right()

            every {
                investmentManagerService.invest(any(), any(), any(), any(), any(), any(), any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.CREATED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            every {
                transactionService.save(any())
            } just Runs

            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.COMPLETED,
                fixedIncomeProduct = fixedIncomeProduct,
                errorMessage = null,
                maturityDate = getLocalDate().plusYears(1),
                positionId = FixedIncomePositionId("POSITION_1"),
            ).right()

            val result = checkout.execute(transaction = transaction)

            result.status shouldBe TransactionStatus.COMPLETED
            val settlementOperation = result.settlementData.settlementOperation
            settlementOperation.shouldBeTypeOf<GoalInvestmentSettlementOperation>()

            settlementOperation.investmentRequestId shouldBe InvestmentManagerExternalId("123")
            settlementOperation.status shouldBe InvestmentManagerRequestStatus.COMPLETED
            settlementOperation.amount shouldBe investment.amountTotal
            settlementOperation.errorDescription shouldBe ""
            settlementOperation.provider shouldBe FixedIncomeProvider.ARBI
            settlementOperation.product shouldBe fixedIncomeProduct

            with(result.paymentData.toSingle().payment) {
                this.shouldBeTypeOf<GoalInvestmentAuthorization>()
                status shouldBe InvestmentManagerRequestStatus.COMPLETED
            }
            verify {
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.DONE
                        it.paidAt.shouldNotBeNull()
                    },
                )
            }
        }
    }

    @Nested
    @DisplayName("RetryTransaction")
    inner class RetryTransactionTest {
        private val checkoutLocator = mockk<CheckoutLocator> {
            every {
                getCheckout(any())
            } returns checkout

            every {
                transactionService.save(any())
            } just Runs

            every {
                transactionService.notify(any())
            } just Runs
        }
        private val billEventPublisher = mockk<BillEventPublisher>() {
            every {
                publish(any(), any())
            } just Runs
        }
        private val notifyReceiptService = mockk<NotifyReceiptService>()
        private val internalLock = mockk<InternalLock>() {
            every {
                acquireLock(any())
            } returns mockk(relaxed = true)
        }

        private val retryTransaction = RetryTransaction(
            transactionService = transactionService,
            checkoutLocator = checkoutLocator,
            settlementRetry = SettlementRetry(
                billEventPublisher = billEventPublisher,
                transactionService = transactionService,
                checkoutLocator = checkoutLocator,
                notifyReceiptService = notifyReceiptService,
            ),
            rollbackTransaction = RollbackTransaction(
                transactionService = transactionService,
                billEventPublisher = billEventPublisher,
                checkoutLocator = checkoutLocator,
                paymentFailedScheduleResolver = mockk() {
                    every { resolvePostPaymentFailedEvent(any()) } just Runs
                },
            ),
            lockProvider = internalLock,
            retryPolicyConfiguration = mockk<RetryPolicyConfiguration> {
                every { shouldUseRetryForever() } returns false
            },
        )

        @BeforeEach
        fun setup() {
            every {
                transactionService.findTransactionById(transaction.id)
            } returns transaction.copy(
                paymentData = transaction.paymentData.toSingle().copy(
                    payment = GoalInvestmentAuthorization(
                        operationId = GoalRequestOperationId("OPERATION_1"),
                        status = InvestmentManagerRequestStatus.CREATED,
                        amount = investment.amountTotal,
                        errorDescription = "",
                    ),
                ),
                settlementData = transaction.settlementData.copy(
                    settlementOperation = GoalInvestmentSettlementOperation(
                        operationId = GoalRequestOperationId("OPERATION_1"),
                        investmentRequestId = InvestmentManagerExternalId("123"),
                        status = InvestmentManagerRequestStatus.CREATED,
                        amount = investment.amountTotal,
                        errorDescription = "",
                        provider = FixedIncomeProvider.ARBI,
                        product = null,
                        maturityDate = null,
                        positionId = null,
                    ),
                ),
            )
        }

        @Test
        fun `should return failure when lock cannot be acquired`() {
            every {
                internalLock.acquireLock(any())
            } returns null

            retryTransaction.retryTransaction(transaction.id)
                .shouldBeFailure<CouldNotAcquireLockException>()
        }

        @Test
        fun `should acquire and release lock when executing transaction`() {
            val mockLock = mockk<SimpleLock>(relaxed = true)
            every {
                internalLock.acquireLock(any())
            } returns mockLock
            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } throws RuntimeException("Test exception")

            retryTransaction.retryTransaction(transaction.id).shouldBeFailure<RuntimeException>()

            verify(exactly = 1) { internalLock.acquireLock(transaction.id.value) }
            verify(exactly = 1) { mockLock.unlock() }
        }

        @Test
        fun `transacao deve continuar como PROCESSING quando não conseguir consultar o status do investimento`() {
            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns InvestmentManagerWithInvestmentResultException(
                result = FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    maturityDate = null,
                    positionId = null,
                ),
                throwable = IllegalStateException("FAKE"),
            ).left()

            retryTransaction.retryTransaction(transaction.id).map { fail("deveria ser left") }.getOrElse {
                it.shouldBeTypeOf<TransactionProcessingException>()
            }

            verify(exactly = 0) {
                transactionService.save(any())
                goalInvestmentRepository.save(any())
            }
        }

        @Test
        fun `transacao deve continuar como PROCESSING o status do investimento for REQUESTED ou CREATED`() {
            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.REQUESTED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            retryTransaction.retryTransaction(transaction.id).map { fail("deveria ser left") }.getOrElse {
                it.shouldBeTypeOf<TransactionProcessingException>()
            }

            val slot = slot<Transaction>()
            verify(exactly = 1) {
                transactionService.save(capture(slot))
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.PROCESSING
                    },
                )
            }

            slot.captured.status shouldBe TransactionStatus.PROCESSING
        }

        @Test
        fun `transacao deve falhar se o status do investimento for FAILED`() {
            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.FAILED,
                fixedIncomeProduct = null,
                errorMessage = null,
                maturityDate = null,
                positionId = null,
            ).right()

            retryTransaction.retryTransaction(transaction.id).getOrElse {
                fail("deveria ser right")
            }

            val slot = mutableListOf<Transaction>()
            val eventSlot = slot<BillEvent>()
            verify {
                transactionService.save(capture(slot))
                billEventPublisher.publish(
                    investment,
                    capture(eventSlot),
                )
            }

            slot.last().status shouldBe TransactionStatus.FAILED
            eventSlot.captured.shouldBeTypeOf<PaymentFailed>()
            verify {
                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.FAILED
                    },
                )
            }
        }

        @Test
        fun `transacao deve completar se o status do investimento for COMPLETED`() {
            every {
                investmentManagerService.checkInvestmentRequestStatus(any())
            } returns FixedIncomeInvestmentRequestResult(
                operationId = GoalRequestOperationId("OPERATION_1"),
                externalId = InvestmentManagerExternalId("123"),
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.COMPLETED,
                fixedIncomeProduct = fixedIncomeProduct,
                errorMessage = null,
                maturityDate = getLocalDate().plusYears(1),
                positionId = FixedIncomePositionId("POSITION_1"),
            ).right()

            every {
                notifyReceiptService.notifyWithAsyncRetry(any())
            } just Runs

            retryTransaction.retryTransaction(transaction.id).getOrElse {
                fail("deveria ser right")
            }

            val slot = mutableListOf<Transaction>()
            val eventSlot = mutableListOf<BillEvent>()

            verify {
                transactionService.save(capture(slot))
                billEventPublisher.publish(
                    investment,
                    capture(eventSlot),
                )

                notifyReceiptService.notifyWithAsyncRetry(any())

                goalInvestmentRepository.save(
                    withArg {
                        it.status shouldBe GoalInvestmentStatus.DONE
                        it.paidAt.shouldNotBeNull()
                    },
                )
            }

            slot.last().status shouldBe TransactionStatus.COMPLETED
            eventSlot.size shouldBe 2
            eventSlot[0].shouldBeTypeOf<BillPaid>()
            eventSlot[1].shouldBeTypeOf<BillPaymentScheduleCanceled>()
        }

        @Nested
        @DisplayName("shouldUseRetryForever = true")
        inner class ShouldUseRetryForeverTrueTest {

            private fun createRetryTransactionWithRetryForeverEnabled(): RetryTransaction {
                val retryPolicyConfig = mockk<RetryPolicyConfiguration> {
                    every { shouldUseRetryForever() } returns true
                }

                return RetryTransaction(
                    transactionService = transactionService,
                    checkoutLocator = <EMAIL>,
                    settlementRetry = SettlementRetry(
                        billEventPublisher = <EMAIL>,
                        transactionService = transactionService,
                        checkoutLocator = <EMAIL>,
                        notifyReceiptService = <EMAIL>,
                    ),
                    rollbackTransaction = RollbackTransaction(
                        transactionService = transactionService,
                        billEventPublisher = <EMAIL>,
                        checkoutLocator = <EMAIL>,
                        paymentFailedScheduleResolver = mockk {
                            every { resolvePostPaymentFailedEvent(any()) } just Runs
                        },
                    ),
                    lockProvider = internalLock,
                    retryPolicyConfiguration = retryPolicyConfig,
                )
            }

            @Test
            fun `deve continuar tentando quando shouldUseRetryForever for true e status for PROCESSING`() {
                val retryTransaction = createRetryTransactionWithRetryForeverEnabled()

                every {
                    investmentManagerService.checkInvestmentRequestStatus(any())
                } returns FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.REQUESTED,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    maturityDate = null,
                    positionId = null,
                ).right()

                retryTransaction.retryTransaction(transaction.id).map { fail("deveria ser left") }.getOrElse {
                    it.shouldBeTypeOf<RetryForeverTransactionException>()
                }

                val slot = slot<Transaction>()
                verify(exactly = 1) {
                    transactionService.save(capture(slot))
                    goalInvestmentRepository.save(
                        withArg {
                            it.status shouldBe GoalInvestmentStatus.PROCESSING
                        },
                    )
                }

                slot.captured.status shouldBe TransactionStatus.PROCESSING
            }

            @Test
            fun `deve falhar normalmente quando shouldUseRetryForever for true mas status for FAILED`() {
                val retryTransaction = createRetryTransactionWithRetryForeverEnabled()

                every {
                    investmentManagerService.checkInvestmentRequestStatus(any())
                } returns FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.FAILED,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    maturityDate = null,
                    positionId = null,
                ).right()

                retryTransaction.retryTransaction(transaction.id).getOrElse {
                    fail("deveria ser right")
                }

                val slot = mutableListOf<Transaction>()
                val eventSlot = slot<BillEvent>()
                verify {
                    transactionService.save(capture(slot))
                    billEventPublisher.publish(
                        investment,
                        capture(eventSlot),
                    )
                }

                slot.last().status shouldBe TransactionStatus.FAILED
                eventSlot.captured.shouldBeTypeOf<PaymentFailed>()
                verify {
                    goalInvestmentRepository.save(
                        withArg {
                            it.status shouldBe GoalInvestmentStatus.FAILED
                        },
                    )
                }
            }

            @Test
            fun `deve completar normalmente quando shouldUseRetryForever for true e status for COMPLETED`() {
                val retryTransaction = createRetryTransactionWithRetryForeverEnabled()

                every {
                    investmentManagerService.checkInvestmentRequestStatus(any())
                } returns FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.COMPLETED,
                    fixedIncomeProduct = fixedIncomeProduct,
                    errorMessage = null,
                    maturityDate = getLocalDate().plusYears(1),
                    positionId = FixedIncomePositionId("POSITION_1"),
                ).right()

                every {
                    notifyReceiptService.notifyWithAsyncRetry(any())
                } just Runs

                retryTransaction.retryTransaction(transaction.id).getOrElse {
                    fail("deveria ser right")
                }

                val slot = mutableListOf<Transaction>()
                val eventSlot = mutableListOf<BillEvent>()

                verify {
                    transactionService.save(capture(slot))
                    billEventPublisher.publish(
                        investment,
                        capture(eventSlot),
                    )

                    notifyReceiptService.notifyWithAsyncRetry(any())

                    goalInvestmentRepository.save(
                        withArg {
                            it.status shouldBe GoalInvestmentStatus.DONE
                            it.paidAt.shouldNotBeNull()
                        },
                    )
                }

                slot.last().status shouldBe TransactionStatus.COMPLETED
                eventSlot.size shouldBe 2
                eventSlot[0].shouldBeTypeOf<BillPaid>()
                eventSlot[1].shouldBeTypeOf<BillPaymentScheduleCanceled>()
            }
        }

        @Nested
        @DisplayName("shouldUseRetryForever = false")
        inner class ShouldUseRetryForeverFalseTest {

            private fun createRetryTransactionWithRetryForeverDisabled(): RetryTransaction {
                val retryPolicyConfig = mockk<RetryPolicyConfiguration> {
                    every { shouldUseRetryForever() } returns false
                }

                return RetryTransaction(
                    transactionService = transactionService,
                    checkoutLocator = <EMAIL>,
                    settlementRetry = SettlementRetry(
                        billEventPublisher = <EMAIL>,
                        transactionService = transactionService,
                        checkoutLocator = <EMAIL>,
                        notifyReceiptService = <EMAIL>,
                    ),
                    rollbackTransaction = RollbackTransaction(
                        transactionService = transactionService,
                        billEventPublisher = <EMAIL>,
                        checkoutLocator = <EMAIL>,
                        paymentFailedScheduleResolver = mockk {
                            every { resolvePostPaymentFailedEvent(any()) } just Runs
                        },
                    ),
                    lockProvider = internalLock,
                    retryPolicyConfiguration = retryPolicyConfig,
                )
            }

            @Test
            fun `deve continuar tentando quando shouldUseRetryForever for false e status for PROCESSING`() {
                val retryTransaction = createRetryTransactionWithRetryForeverDisabled()

                every {
                    investmentManagerService.checkInvestmentRequestStatus(any())
                } returns FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.REQUESTED,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    maturityDate = null,
                    positionId = null,
                ).right()

                retryTransaction.retryTransaction(transaction.id).map { fail("deveria ser left") }.getOrElse {
                    it.shouldBeTypeOf<TransactionProcessingException>()
                }

                val slot = slot<Transaction>()
                verify(exactly = 1) {
                    transactionService.save(capture(slot))
                    goalInvestmentRepository.save(
                        withArg {
                            it.status shouldBe GoalInvestmentStatus.PROCESSING
                        },
                    )
                }

                slot.captured.status shouldBe TransactionStatus.PROCESSING
            }

            @Test
            fun `deve falhar normalmente quando shouldUseRetryForever for false e status for FAILED`() {
                val retryTransaction = createRetryTransactionWithRetryForeverDisabled()

                every {
                    investmentManagerService.checkInvestmentRequestStatus(any())
                } returns FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.FAILED,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    maturityDate = null,
                    positionId = null,
                ).right()

                retryTransaction.retryTransaction(transaction.id).getOrElse {
                    fail("deveria ser right")
                }

                val slot = mutableListOf<Transaction>()
                val eventSlot = slot<BillEvent>()
                verify {
                    transactionService.save(capture(slot))
                    billEventPublisher.publish(
                        investment,
                        capture(eventSlot),
                    )
                }

                slot.last().status shouldBe TransactionStatus.FAILED
                eventSlot.captured.shouldBeTypeOf<PaymentFailed>()
                verify {
                    goalInvestmentRepository.save(
                        withArg {
                            it.status shouldBe GoalInvestmentStatus.FAILED
                        },
                    )
                }
            }

            @Test
            fun `deve completar normalmente quando shouldUseRetryForever for false e status for COMPLETED`() {
                val retryTransaction = createRetryTransactionWithRetryForeverDisabled()

                every {
                    investmentManagerService.checkInvestmentRequestStatus(any())
                } returns FixedIncomeInvestmentRequestResult(
                    operationId = GoalRequestOperationId("OPERATION_1"),
                    externalId = InvestmentManagerExternalId("123"),
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.COMPLETED,
                    fixedIncomeProduct = fixedIncomeProduct,
                    errorMessage = null,
                    maturityDate = getLocalDate().plusYears(1),
                    positionId = FixedIncomePositionId("POSITION_1"),
                ).right()

                every {
                    notifyReceiptService.notifyWithAsyncRetry(any())
                } just Runs

                retryTransaction.retryTransaction(transaction.id).getOrElse {
                    fail("deveria ser right")
                }

                val slot = mutableListOf<Transaction>()
                val eventSlot = mutableListOf<BillEvent>()

                verify {
                    transactionService.save(capture(slot))
                    billEventPublisher.publish(
                        investment,
                        capture(eventSlot),
                    )

                    notifyReceiptService.notifyWithAsyncRetry(any())

                    goalInvestmentRepository.save(
                        withArg {
                            it.status shouldBe GoalInvestmentStatus.DONE
                            it.paidAt.shouldNotBeNull()
                        },
                    )
                }

                slot.last().status shouldBe TransactionStatus.COMPLETED
                eventSlot.size shouldBe 2
                eventSlot[0].shouldBeTypeOf<BillPaid>()
                eventSlot[1].shouldBeTypeOf<BillPaymentScheduleCanceled>()
            }
        }
    }
}