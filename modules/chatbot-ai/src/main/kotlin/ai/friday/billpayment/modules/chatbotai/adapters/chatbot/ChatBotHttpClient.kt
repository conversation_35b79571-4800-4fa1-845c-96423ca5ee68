package ai.friday.billpayment.modules.chatbotai.adapters.chatbot

import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.ClientFilter
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.RequestFilter
import io.micronaut.http.client.annotation.Client
import jakarta.inject.Singleton
import reactor.core.publisher.Mono

private const val CLIENT = "chatbot"

@Singleton
@ClientFilter(serviceId = [CLIENT])
class BasicAuthClientFilter(
    private val configuration: ChatbotConfiguration,
    @Property(name = "tenant.id") private val tenantId: String,
) {

    @RequestFilter
    fun doFilter(request: MutableHttpRequest<*>) {
        request.basicAuth(configuration.clientid, configuration.secret)
        request.header("X-TENANT-ID", tenantId)
    }
}

@Client(CLIENT)
interface ChatBotHttpClient {
    @Post("/queue/{name}")
    fun send(name: String, @Body message: Any): Mono<HttpResponse<String>>
}