package ai.friday.billpayment.app.payment

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.CreditCardChargebackTrackDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UniqueConstraintDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.app.payment.transaction.PaymentFailedScheduleResolver
import ai.friday.billpayment.app.payment.transaction.RetryPolicyConfiguration
import ai.friday.billpayment.app.payment.transaction.RetryTransaction
import ai.friday.billpayment.app.payment.transaction.RollbackTransaction
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.LocalDbCreationRule.Companion.dynamoDB
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.paymentMethodId2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.result.shouldBeFailure
import io.kotest.matchers.shouldBe
import io.micronaut.http.client.exceptions.HttpClientException
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class InvoiceRetryTransactionTest {

    private val updateBillService: UpdateBillService = mockk(relaxed = true)

    private val billEventPublisher: BillEventPublisher = mockk(relaxed = true)

    private val tedService: TEDService = mockk()

    private val invoiceCheckout = BalanceDirectInvoiceCheckout(tedService)

    private val checkoutLocator = DefaultCheckoutLocator(
        balancePixCheckout = mockk(),
        balanceDirectInvoiceCheckout = invoiceCheckout,
        boletoCheckout = mockk(),
        asyncSettlementBoletoCheckout = mockk(),
        featureConfiguration = mockk(),
        investmentCheckout = mockk(),
    )

    private val bankAccount = internalBankAccount

    private val notificationMock: NotificationAdapter = mockk(relaxed = true)

    private val successBankTransfer = BankTransfer(
        status = BankOperationStatus.SUCCESS,
        amount = invoiceAdded.amountTotal,
        gateway = FinancialServiceGateway.ARBI,
    )

    private val errorDescription = "Generic error"

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val billEventDAO = BillEventDynamoDAO(enhancedClient)
    private val uniqueConstraintDAO = UniqueConstraintDynamoDAO(enhancedClient)
    private val transactionDynamo = TransactionDynamo(enhancedClient)
    private val billEventRepository = BillEventDBRepository(
        billEventDAO = billEventDAO,
        uniqueConstraintDAO = uniqueConstraintDAO,
        featureConfiguration = allFalseFeatureConfiguration,
        transactionDynamo = transactionDynamo,
    )

    private val accountDAO = AccountDynamoDAO(enhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(enhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(enhancedClient)
    private val nsuDAO = NSUDynamoDAO(enhancedClient)
    private val transactionDAO = TransactionDynamoDAO(enhancedClient)
    private val creditCardChargebackTrackDAO = CreditCardChargebackTrackDynamoDAO(enhancedClient)

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = updateBillService,
        notificationAdapter = notificationMock,
        walletService = mockk(),
        transactionRepository = DynamoDbTransactionRepository(
            transactionDAO = transactionDAO,
            creditCardChargebackTrackDAO = creditCardChargebackTrackDAO,
            accountRepository = accountRepository,
            transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk(), transactionDynamo),
        ),
    )

    private val notifyReceiptService = mockk<NotifyReceiptService>()
    private val checkStatusBeforeRetry = SettlementRetry(billEventPublisher, transactionService, checkoutLocator, notifyReceiptService)

    private val paymentFailedScheduleResolver = mockk<PaymentFailedScheduleResolver>(relaxed = true)

    private val internalLock: InternalLock = mockk(relaxed = true)

    private val retryPolicyConfig: RetryPolicyConfiguration = mockk()

    private val rollbackTransaction = RollbackTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
        checkoutLocator = checkoutLocator,
        paymentFailedScheduleResolver = paymentFailedScheduleResolver,
    )

    private val service = RetryTransaction(
        transactionService = transactionService,
        checkoutLocator = checkoutLocator,
        settlementRetry = checkStatusBeforeRetry,
        rollbackTransaction = rollbackTransaction,
        lockProvider = internalLock,
        retryPolicyConfiguration = retryPolicyConfig,
    )

    private val balance = AccountPaymentMethod(
        id = paymentMethodId2,
        accountId = AccountId(ACCOUNT_ID),
        status = AccountPaymentMethodStatus.ACTIVE,
        method = bankAccount,
        created = getZonedDateTime(),
    )

    private val invoiceTransaction = Transaction(
        type = TransactionType.DIRECT_INVOICE,
        payer = ACCOUNT.toPayer(),
        paymentData = createSinglePaymentDataWithBalance(
            balance,
            invoiceBill.amountTotal,
        ),
        settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal),
        nsu = 1,
        actionSource = ActionSource.Scheduled,
        walletId = invoiceBill.walletId,
    )

    private val arbiInvoiceSettlementData = SettlementData(
        invoiceBill,
        0,
        invoiceBill.amountTotal,
        BankTransfer(
            operationId = BankOperationId("123"),
            gateway = FinancialServiceGateway.ARBI,
            status = BankOperationStatus.UNKNOWN,
            amount = invoiceTransaction.settlementData.totalAmount,
            authentication = "",
            errorDescription = errorDescription,
        ),
    )

    private val arbiInvoiceTransaction = Transaction(
        type = TransactionType.DIRECT_INVOICE,
        payer = ACCOUNT.toPayer(),
        paymentData = createSinglePaymentDataWithBalance(
            balance,
            invoiceBill.amountTotal,
        ),
        settlementData = arbiInvoiceSettlementData,
        nsu = 1,
        actionSource = ActionSource.System,
        walletId = invoiceBill.walletId,
    )

    @Test
    fun `should return failure when lock cannot be acquired`() {
        every { internalLock.acquireLock(any()) } returns null

        service.retryTransaction(invoiceTransaction.id)
            .shouldBeFailure<CouldNotAcquireLockException>()
    }

    @Test
    fun `should acquire and release lock when executing transaction`() {
        val mockLock = mockk<SimpleLock>(relaxed = true)
        every { internalLock.acquireLock(any()) } returns mockLock
        buildInvoiceTransaction(BankOperationStatus.INVALID_DATA, errorDescription)

        transactionService.save(invoiceTransaction)

        service.retryTransaction(invoiceTransaction.id)

        verify(exactly = 1) { internalLock.acquireLock(invoiceTransaction.id.value) }
        verify(exactly = 1) { mockLock.unlock() }
    }

    @Test
    fun `should release lock even when exception is thrown`() {
        val mockLock = mockk<SimpleLock>(relaxed = true)
        every { internalLock.acquireLock(any()) } returns mockLock
        every {
            tedService.checkSettlementStatus(any())
        } throws RuntimeException("Test exception")

        buildInvoiceTransaction(BankOperationStatus.ERROR, errorDescription)
        transactionService.save(invoiceTransaction)

        service.retryTransaction(invoiceTransaction.id).shouldBeFailure<RuntimeException>()

        verify(exactly = 1) { mockLock.unlock() }
    }

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        billEventRepository.save(invoiceAdded)
        billEventRepository.save(invoicePaymentStarted)
        with(bankAccount) { loadBalancePaymentMethod(accountRepository, bankNo, routingNo, accountNo.toBigInteger(), accountDv) }
    }

    @Test
    fun `should publish bill payment scheduled canceled when scheduled bill is not retryable on retry`() {
        billEventRepository.save(invoicePaymentScheduled)
        buildInvoiceTransaction(BankOperationStatus.INVALID_DATA, errorDescription)

        service.retryTransaction(invoiceTransaction.id)

        verify {
            paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(any())
        }
    }

    @Test
    fun `should not publish bill payment scheduled canceled when scheduled bill IS retryable on retry`() {
        billEventRepository.save(invoicePaymentScheduled)
        buildInvoiceTransaction(BankOperationStatus.INSUFFICIENT_FUNDS, errorDescription)

        service.retryTransaction(invoiceTransaction.id)

        verify(exactly = 0) {
            billEventPublisher.publish(
                invoiceTransaction.settlementData.getTarget(),
                ofType(BillPaymentScheduleCanceled::class),
            )
        }
    }

    @Test
    fun `should confirm transaction retry when it fails on settlement transfer and check status is success`() {
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.Success()
        buildInvoiceTransaction(BankOperationStatus.ERROR, errorDescription)
        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        service.retryTransaction(invoiceTransaction.id)

        with(invoiceTransaction) {
            val bill = settlementData.getTarget<Bill>()
            verify(exactly = 0) {
                billEventPublisher.publish(bill, ofType(BillPaymentScheduleCanceled::class))
            }
            verify {
                billEventPublisher.publish(bill, ofType(BillPaid::class))
                notifyReceiptService.notifyWithAsyncRetry(any())
            }
        }
        assertTransaction(TransactionStatus.COMPLETED, BankOperationStatus.SUCCESS, BankOperationStatus.SUCCESS)
    }

    @Test
    fun `should publish bill payment schedule canceled when settlement is success and bill is scheduled`() {
        billEventRepository.save(invoicePaymentScheduled)
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.Success()
        buildInvoiceTransaction(BankOperationStatus.ERROR, errorDescription)
        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        service.retryTransaction(invoiceTransaction.id)

        verify {
            billEventPublisher.publish(
                eq(invoiceTransaction.settlementData.getTarget()),
                ofType(BillPaymentScheduleCanceled::class),
            )
            notifyReceiptService.notifyWithAsyncRetry(any())
        }
    }

    @Test
    fun `should keep retrying when it fails on settlement transfer and checkstatus is failure`() {
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.Failure(errorDescription)
        buildInvoiceTransaction(BankOperationStatus.ERROR, "Server error")

        service.retryTransaction(invoiceTransaction.id)

        assertTransaction(TransactionStatus.PROCESSING, BankOperationStatus.SUCCESS, BankOperationStatus.ERROR)

        verify(exactly = 0) {
            billEventPublisher.publish(any(), any())
        }
    }

    @Test
    fun `should throw exception when check status fails on retry`() {
        every { tedService.checkSettlementStatus(any()) } throws HttpClientException("Error")
        buildInvoiceTransaction(BankOperationStatus.ERROR, "Server error")

        service.retryTransaction(invoiceTransaction.id).shouldBeFailure<HttpClientException>()

        verify {
            tedService.checkSettlementStatus(any())
            updateBillService wasNot called
            notificationMock wasNot called
        }
    }

    @Test
    fun `should not fail transaction when check settlement returns error on a processing transaction after more than 1 hour`() {
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.Failure("Error")
        transactionService.save(arbiInvoiceTransaction.copy(created = getZonedDateTime().minusHours(2)))

        service.retryTransaction(arbiInvoiceTransaction.id).shouldBeFailure<TransactionRollbackException>()

        verify(exactly = 0) {
            billEventPublisher.publish(any(), ofType(PaymentFailed::class))
        }

        transactionService.findTransactionById(arbiInvoiceTransaction.id).status shouldBe TransactionStatus.PROCESSING
    }

    @Test
    fun `should not fail transaction when check settlement returns invalid data on a processing transaction after more than one hour`() {
        billEventRepository.save(invoicePaymentScheduled)
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.InvalidData("Invalid bank")
        transactionService.save(arbiInvoiceTransaction.copy(created = getZonedDateTime().minusHours(2)))

        service.retryTransaction(arbiInvoiceTransaction.id).shouldBeFailure<TransactionRollbackException>()

        verify(exactly = 0) {
            billEventPublisher.publish(any(), ofType(PaymentFailed::class))
            paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(any())
        }

        val transaction = transactionService.findTransactionById(arbiInvoiceTransaction.id)
        transaction.status shouldBe TransactionStatus.PROCESSING
        transaction.isRetryable() shouldBe true
    }

    @Test
    fun `should succeed transaction on Arbi invoice transaction retry`() {
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.Success()
        transactionService.save(arbiInvoiceTransaction)

        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        service.retryTransaction(arbiInvoiceTransaction.id)

        verify {
            billEventPublisher.publish(any(), ofType(BillPaid::class))
            notifyReceiptService.notifyWithAsyncRetry(any())
        }

        transactionService.findTransactionById(arbiInvoiceTransaction.id).status shouldBe TransactionStatus.COMPLETED
    }

    @Test
    fun `should stay processing transaction on Arbi invoice transaction retry`() {
        every { tedService.checkSettlementStatus(any()) } returns SettlementStatus.Error
        transactionService.save(arbiInvoiceTransaction)

        service.retryTransaction(arbiInvoiceTransaction.id).shouldBeFailure<TransactionRollbackException>()

        verify {
            updateBillService wasNot called
        }

        transactionService.findTransactionById(arbiInvoiceTransaction.id).status shouldBe TransactionStatus.PROCESSING
    }

    private fun assertTransaction(
        transaction: Transaction,
        transactionStatus: TransactionStatus,
        paymentBankOperationStatus: BankOperationStatus,
        settlementBankOperationStatus: BankOperationStatus,
    ) {
        transaction.status shouldBe transactionStatus
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe paymentBankOperationStatus
        transaction.settlementData.getOperation<BankTransfer>().status shouldBe settlementBankOperationStatus
    }

    private fun assertTransaction(
        transactionStatus: TransactionStatus,
        paymentBankOperationStatus: BankOperationStatus,
        settlementBankOperationStatus: BankOperationStatus,
    ) {
        val transactionDB = transactionService.findTransactionById(invoiceTransaction.id)
        assertTransaction(transactionDB, transactionStatus, paymentBankOperationStatus, settlementBankOperationStatus)
    }

    private fun buildInvoiceTransaction(settlementBankOperationStatus: BankOperationStatus, errorDescription: String) {
        invoiceTransaction.apply {
            settlementData.settlementOperation = BankTransfer(
                operationId = BankOperationId("123"),
                gateway = FinancialServiceGateway.CELCOIN,
                status = settlementBankOperationStatus,
                amount = invoiceTransaction.settlementData.totalAmount,
                authentication = "",
                errorDescription = errorDescription,
            )
            paymentData.toSingle().payment = BalanceAuthorization(
                operationId = successBankTransfer.operationId,
                status = BankOperationStatus.SUCCESS,
                amount = invoiceTransaction.settlementData.totalAmount,
                paymentGateway = FinancialServiceGateway.ARBI,
            )
        }
        transactionService.save(invoiceTransaction)
    }
}