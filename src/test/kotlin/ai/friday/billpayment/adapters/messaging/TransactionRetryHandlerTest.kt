package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.payment.RetryForeverTransactionException
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionRollbackException
import ai.friday.billpayment.app.payment.increaseRetryInterval
import ai.friday.billpayment.app.payment.transaction.RetryTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionProcessingException
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

class TransactionRetryHandlerTest {

    private val amazonSQS: SqsClient = mockk()
    private val configuration: SQSMessageHandlerConfiguration = mockk(relaxed = true)
    private val retryTransaction: RetryTransaction = mockk()
    private val cashInExecutor: CashInExecutor = mockk()
    private val messagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)

    private val transactionRepository = mockk<TransactionRepository>()
    private val transactionRetryHandler =
        TransactionRetryHandler(amazonSQS, configuration, retryTransaction, cashInExecutor, messagePublisher, transactionRepository)

    private val transactionId = UUID.randomUUID().toString()

    private val firstMessageTimestamp = getZonedDateTime()

    private val retryTransactionParams = mapOf(
        "transactionId" to transactionId,
        "eventType" to retryTransactionEventType,
    )

    private val retryCashinTransactionParams = mapOf(
        "transactionId" to transactionId,
        "eventType" to retryCashinTransactionEventType,
    )

    private val receipt = "1234567890"
    private val retryTransactionMessage =
        Message.builder().body(ObjectMapper().writeValueAsString(retryTransactionParams)).receiptHandle(receipt).build()
    private val retryCashinTransactionMessage =
        Message.builder().body(ObjectMapper().writeValueAsString(retryCashinTransactionParams)).receiptHandle(receipt)
            .build()

    @Test
    fun `should not delete message on retryTransaction exception`() {
        val result = transactionRetryHandler.handleError(retryTransactionMessage, TransactionRollbackException())
        result.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `should call retry transaction on retryTransaction event type and delete message when retry is completed`() {
        every {
            retryTransaction.retryTransaction(TransactionId(transactionId))
        } returns Result.success(Unit)

        val result = transactionRetryHandler.handleMessage(retryTransactionMessage)

        verify(exactly = 1) {
            retryTransaction.retryTransaction(TransactionId(transactionId))
        }
        result.shouldDeleteMessage shouldBe true
    }

    @Test
    fun `should call retry transaction on retryTransaction event type and keep message when retry is processing`() {
        every {
            retryTransaction.retryTransaction(TransactionId(transactionId))
        } returns Result.failure(TransactionProcessingException("Tentativa de reprocessar uma transação com o status de PROCESSING"))

        val result = transactionRetryHandler.handleMessage(retryTransactionMessage)

        verify(exactly = 1) {
            retryTransaction.retryTransaction(TransactionId(transactionId))
        }
        result.shouldDeleteMessage shouldBe false
    }

    @Test
    fun `should call retry transaction on retryCashinTransaction event type`() {
        every { cashInExecutor.retryTransaction(any()) } returns Result.success(Unit)
        val result = transactionRetryHandler.handleMessage(retryCashinTransactionMessage)

        verify(exactly = 1) {
            cashInExecutor.retryTransaction(TransactionId(transactionId))
        }
        result.shouldDeleteMessage shouldBe true
    }

    @ParameterizedTest
    @CsvSource(
        "30, 10",
        "60, 10",
        "299, 30",
        "300, 30",
        "301, 60",
        "3600, 300",
        "3601, 600",
        "7200, 600",
    )
    fun `deve retentar com delay quando ocorrer erro ao processar a mensagem e o retorno for do tipo RepublishWithInterval`(elapsedSeconds: Int, delta: Int) {
        every { cashInExecutor.retryTransaction(any()) } returns Result.failure(RetryForeverTransactionException(TransactionId(transactionId), increaseRetryInterval))

        every {
            transactionRepository.findById(TransactionId(transactionId))
        } returns mockk() {
            every { created } returns firstMessageTimestamp
        }

        val result = withGivenDateTime(firstMessageTimestamp.plusSeconds(elapsedSeconds.toLong())) {
            transactionRetryHandler.handleMessage(retryCashinTransactionMessage)
        }

        val messageSlot = slot<QueueMessage>()
        verify(exactly = 1) {
            cashInExecutor.retryTransaction(TransactionId(transactionId))
            messagePublisher.sendMessage(capture(messageSlot))
        }

        messageSlot.captured.delaySeconds shouldBe delta
        messageSlot.captured.jsonObject shouldBe retryCashinTransactionMessage.body()
        result.shouldDeleteMessage shouldBe true
    }
}