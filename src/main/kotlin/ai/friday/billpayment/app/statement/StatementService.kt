package ai.friday.billpayment.app.statement

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.account.isBetaGroup
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.CreditCustomerAccountService
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BonusCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCredit
import ai.friday.billpayment.app.integrations.CustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.CustomerAccountCreditReason
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.RefundedBillCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.RefundedSubscriptionCustomerAccountCreditDetails
import ai.friday.billpayment.app.integrations.Statement
import ai.friday.billpayment.app.integrations.StatementItemConverter
import ai.friday.billpayment.app.integrations.TransactionRollbackCustomerAccountCreditDetails
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateFormatBR
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.message.ForwardMessageException
import java.security.MessageDigest
import java.time.LocalDate
import java.time.LocalTime
import java.time.Month
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.abs
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
open class StatementService(
    private val internalBankRepository: InternalBankRepository,
    private val billRepository: BillRepository,
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val notificationAdapter: NotificationAdapter,
    private val statementItemConverter: StatementItemConverter,
    private val accountStatementAdapter: AccountStatementAdapter,
    private val sqsMessagePublisher: MessagePublisher,
    internal val walletBillCategoryService: PFMWalletCategoryService,
    private val creditCustomerAccountService: CreditCustomerAccountService,
    @Property(name = "sqs.statementQueueName") private val queueName: String,
    @Property(name = "integrations.arbi.inscricao") private val selfCNPJArbiAccount: String,
    @Property(name = "integrations.arbi.contaLiquidacao") private val contaLiquidacao: String,
    @Property(name = "tenant.displayName") private val displayName: String,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Trace
    open fun findAllCredits(walletId: WalletId, walletMember: Member): Either<StatementError, List<BankStatementItem>> {
        try {
            if (!walletMember.permissions.viewBalance) {
                return emptyList<BankStatementItem>().right()
            }
            val wallet = walletService.findWalletOrNull(walletId) ?: return StatementError.WalletNotFound.left()
            val (_, rawCredits) = internalBankRepository.findAllBankStatementCredits(
                wallet.paymentMethodId,
                LocalDate.EPOCH,
                getLocalDate(),
            ).partitionByAbatementItems(wallet.founder.accountId)
            val (validCredits, _) = rawCredits.partition { it.counterpartDocument != "***********" && it.counterpartName.isNotBlank() || it.type == BankStatementItemType.INVESTMENT_REDEMPTION }

            logger.info(
                append("wallet", walletId.value)
                    .andAppend("accountId", walletMember.accountId.value)
                    .andAppend("paymentMethodId", wallet.paymentMethodId.value)
                    .andAppend("validCredits", validCredits.size),
                "StatementService#findAllCredits",
            )

            return validCredits.map {
                if (it.type == BankStatementItemType.INVESTMENT_REDEMPTION) {
                    val newItem = it as DefaultBankStatementItem
                    newItem.copy(counterpartDocument = wallet.founder.document) // TODO - remover isso depois que o front conhecer o tipo e não precisar mais do documento valido
                } else {
                    it
                }
            }.right()
        } catch (ex: Exception) {
            return StatementError.ServerError.left()
        }
    }

    fun findAllStatementsByDate(
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate, // inclusive
        forceAlpha: Boolean = false,
    ): Either<StatementError, UserStatement> {
        val wallet = walletService.findWalletOrNull(walletId) ?: return StatementError.WalletNotFound.left()
        val balance = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as InternalBankAccount
        return findAllStatementsByDate(wallet, balance, startDate, endDate, forceAlpha)
    }

    private fun findAllStatementsByDate(
        wallet: Wallet,
        balance: InternalBankAccount,
        startDate: LocalDate,
        endDate: LocalDate, // inclusive
        forceAlpha: Boolean,
    ): Either<StatementError, UserStatement> {
        return if (forceAlpha || wallet.founder.accountId.hasEarlyAccess()) {
            alphaFindAllStatementsByDate(wallet = wallet, internalBankAccount = balance, startDate = startDate, endDate = endDate)
        } else {
            defaultFindAllStatementsByDate(wallet = wallet, balance = balance, startDate = startDate, endDate = endDate)
        }
    }

    private fun defaultFindAllStatementsByDate(
        wallet: Wallet,
        balance: InternalBankAccount,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, UserStatement> {
        try {
            val categories = walletBillCategoryService.findWalletCategories(wallet.id)
                .associate { it.categoryId to it.name }

            val bankStatement = accountStatementAdapter.getStatement(
                accountNo = balance.buildFullAccountNumber(),
                document = Document(wallet.founder.document),
                initialDate = startDate,
                endDate = endDate,
            ).ignoreFridayCreditMovements(wallet.founder.accountId)

            val creditItems = bankStatement.items
                .filter { it.flow == BankStatementItemFlow.CREDIT }
                .map { it.toItem() }

            val paidBillItems = billRepository.getPaidBills(wallet.id, startDate.atStartOfDay(brazilTimeZone), endDate.atTime(LocalTime.MAX).atZone(brazilTimeZone))
                .removeCreditCardPayments()
                .removeOpenFinancePayments()
                .map { it.toItem(categories) }

            var currentBalance = bankStatement.initialBalance
            val items = (paidBillItems + creditItems)
                .sortedBy { it.postedAt }
                .map {
                    val absoluteAmount = abs(it.amount)

                    currentBalance += when (it.flow) {
                        BankStatementItemFlow.CREDIT -> Balance(absoluteAmount)
                        BankStatementItemFlow.DEBIT -> Balance(-absoluteAmount)
                    }

                    it.copy(balance = currentBalance)
                }

            return UserStatement(
                walletId = wallet.id,
                startDate = startDate,
                initialBalance = bankStatement.initialBalance,
                statementItems = items,
                endDate = endDate,
                finalBalance = bankStatement.finalBalance,
            ).right()
        } catch (ex: ItemNotFoundException) {
            logger.error(append("exceptionType", "ResourceNotFoundException"), "StatementService#findAllStatementsByDate", ex)
            return StatementError.WalletNotFound.left()
        } catch (ex: ArbiAccountMissingPermissionsException) {
            logger.error(append("exceptionType", "ArbiAccountMissingPermissionsException"), "StatementService#findAllStatementsByDate", ex)
            return StatementError.AccountMissingPermissionError.left()
        } catch (ex: Exception) {
            logger.error(append("exceptionType", "Exception"), "StatementService#findAllStatementsByDate", ex)
            return StatementError.ServerError.left()
        }
    }

    private fun alphaFindAllStatementsByDate(
        wallet: Wallet,
        internalBankAccount: InternalBankAccount,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, UserStatement> {
        val logName = "StatementService#findAllStatementsByDate_ALPHA"
        val markers = append("params.walletId", wallet.id.value)
            .andAppend("params.internalBankAccount", internalBankAccount)
            .andAppend("params.startDate", startDate.format(dateFormat))
            .andAppend("params.endDate", endDate.format(dateFormat))

        try {
            val helper = FindAllStatementsByDateHelper(
                wallet = wallet,
                internalBankAccount = internalBankAccount,
                startDate = startDate,
                endDate = endDate,
            )
            // contas pagas com saldo da conta corrente no período
            markers.andAppend("friday.billsPaid.all", helper.billsPaid.map { it.toLogAttribute() })

            // contas pagas com saldo da conta corrente no período e estornadas
            markers.andAppend("friday.billsPaidAndRefunded.all", helper.billsPaidAndRefunded.map { it.toLogAttribute() })

            // créditos na conta corrente feitos pela friday
            markers.andAppend("friday.customerAccountCredits.all", helper.customerAccountCredits.map { it.toLogAttribute() })

            // o extrato do arbi contém todas as movimentações feitas na conta-corrente do cliente,
            // inclusive TEFs para reserva de saldo durante uma transação de pagamento de conta
            val bankStatement = helper.bankStatement
            markers.andAppend("arbi.balance.initial", bankStatement.initialBalance.amount)
                .andAppend("arbi.balance.checked", bankStatement.checkedBalance.amount)
                .andAppend("arbi.balance.final", bankStatement.finalBalance.amount)

            // e se a transação foi desfeita, tanto a TEF de reserva de saldo (débito)
            // quanto a TEF de liberação do saldo (crédito) devem ser omitidas do extrato
            val (discloseableItems, undiscloseableItems) = bankStatement.partitionDiscloseableItems(helper.customerAccountCredits)
            markers.andAppend("arbi.statementItems.discloseable", discloseableItems.map { it.toLogAttribute() })
                .andAppend("arbi.statementItems.undiscloseable", undiscloseableItems.map { it.toLogAttribute() })

            // todos os items restantes no extrato do Arbi devem ser exibidos, inclusive estornos
            // mas devem ser enriquecidos com informação da conta paga ou do crédito friday e da categoria
            val enrichedItems = helper.enrich(discloseableItems)

            // items que não foram usados para enriquecer o extrato
            // e podem indicar alguma falha na tentativa de parear o extrato arbi com a friday
            markers.andAppend("friday.billsPaid.unused", helper.billsPaid.map { it.toLogAttribute() })
                .andAppend("friday.billsPaidAndRefunded.unused", helper.billsPaidAndRefunded.map { it.toLogAttribute() })
                .andAppend("friday.customerAccountCredits.unused", helper.customerAccountCredits.map { it.toLogAttribute() })

            // calcula o saldo corrente
            var currentBalance = bankStatement.initialBalance
            val enrichedItemsWithBalance = enrichedItems.sortedBy { it.postedAt }.map {
                val absoluteAmount = abs(it.amount)

                currentBalance += when (it.flow) {
                    BankStatementItemFlow.CREDIT -> Balance(absoluteAmount)
                    BankStatementItemFlow.DEBIT -> Balance(-absoluteAmount)
                }

                it.copy(balance = currentBalance)
            }
            markers.andAppend("friday.statementItems", enrichedItemsWithBalance.map { it.toLogAttribute() })

            // o saldo final calculado deve ser o mesmo do Arbi
            if (currentBalance != bankStatement.finalBalance) {
                markers.andAppend("ACTION", "VERIFY")
                    .andAppend("context", "saldo final inconsistente. friday: ${currentBalance.amount} arbi:${bankStatement.finalBalance.amount}")
                logger.warn(markers, logName)
            } else {
                logger.info(markers, logName)
            }

            return UserStatement(
                walletId = wallet.id,
                startDate = startDate,
                initialBalance = bankStatement.initialBalance,
                statementItems = enrichedItemsWithBalance,
                endDate = endDate,
                finalBalance = bankStatement.finalBalance,
            ).right()
        } catch (ex: ItemNotFoundException) {
            logger.error(markers, logName, ex)
            return StatementError.WalletNotFound.left()
        } catch (ex: ArbiAccountMissingPermissionsException) {
            logger.error(markers, logName, ex)
            return StatementError.AccountMissingPermissionError.left()
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            return StatementError.ServerError.left()
        }
    }

    private inner class FindAllStatementsByDateHelper(
        val wallet: Wallet,
        val internalBankAccount: InternalBankAccount,
        val startDate: LocalDate,
        val endDate: LocalDate,
    ) {
        val startDateTime = startDate.atStartOfDay(brazilTimeZone)
        val endDateTime = endDate.atEndOfDay(brazilTimeZone)

        val categories = walletBillCategoryService.findWalletCategories(wallet.id)
            .associate { it.categoryId to it.name }

        val billsPaid = billRepository.getPaidBills(
            walletId = wallet.id,
            startDate = startDateTime,
            endDate = endDateTime,
        ).filterPaidWithBalance().sortedBy { it.paidDate }.toMutableList()

        val billsPaidAndRefunded = creditCustomerAccountService.findRefundedBillByPaymentDate(
            accountPaymentMethodId = wallet.paymentMethodId,
            startDate = startDateTime,
            endDate = endDateTime,
        ).filterPaidWithBalanceAndRefunded().sortedBy { (it.details as RefundedBillCustomerAccountCreditDetails).originalPaidDate }.toMutableList()

        val customerAccountCredits = creditCustomerAccountService.findAllByCreditDate(
            accountPaymentMethodId = wallet.paymentMethodId,
            startDate = startDateTime,
            endDate = endDateTime,
        ).sortedBy { it.createdAt }.toMutableList()

        val bankStatement = accountStatementAdapter.getStatement(
            accountNo = internalBankAccount.buildFullAccountNumber(),
            document = Document(wallet.founder.document),
            initialDate = startDate,
            endDate = endDate,
        )

        fun enrich(statementItems: List<BankStatementItem>) = statementItems.map { item ->
            when (item.flow) {
                // qualquer valor debitado da conta-corrente do cliente
                // deve ser derivado do pagamento de uma conta, mesmo que estornado
                BankStatementItemFlow.DEBIT -> {
                    // tenta encontrar uma conta paga que seja a contra-partida do débito
                    var enrichedItem = billsPaid.findAndRemoveMatchingPayment(item)?.toItem(categories)

                    // se não encontrou uma conta paga
                    if (enrichedItem == null) {
                        // tenta encontrar uma conta estornada que seja a contra-partida do débito
                        // OBS: começamos a registrar os estornos nesse formato em 2025-09-01
                        //      e criamos o histórico somente para contas que estavam ativas
                        //      extrato de contas inativas antes desse período deve cair no alarme abaixo
                        enrichedItem = billsPaidAndRefunded.findAndRemoveMatchingRefundedPayment(item)?.toDebitItem()
                    }

                    // se não encontrou conta nem paga, nem estornada
                    if (enrichedItem == null) {
                        // usa o próprio item do extrato do arbi, sem suprimir nenhum débito indevidamente
                        enrichedItem = item.toItem()

                        // mas como esse caso não deveria acontecer, alarma
                        val markers = append("ACTION", "VERIFY")
                            .andAppend("context", "débito inesperado no extrato Arbi")
                            .andAppend("statementItem", item)
                        logger.warn(markers, "StatementService")
                    }

                    enrichedItem
                }

                // já os créditos podem ser depósitos comuns (realizados pelo próprio cliente ou por terceiros)
                // ou créditos feitos pela própria friday em situações específicas (estorno, reembolso, bonus ou mesmo o finado cashIn por cartão de crédito)
                BankStatementItemFlow.CREDIT -> {
                    var enrichedItem = customerAccountCredits.findAndRemoveMatchingCredit(item)?.toCreditItem(item.counterpartName)

                    if (enrichedItem == null) {
                        // usa o próprio item do extrato do arbi
                        enrichedItem = item.toItem()
                    }

                    enrichedItem
                }
            }
        }
    }

    private fun LocalDate.atEndOfDay(zoneId: ZoneId) = atStartOfDay(zoneId).plusDays(1).minusNanos(1)

    private fun BankStatement.partitionDiscloseableItems(customerAccountCredits: List<CustomerAccountCredit>): Pair<List<BankStatementItem>, List<BankStatementItem>> {
        val discloseableItems = items.sortedBy { it.lastUpdate }.toMutableList()
        val undiscloseableItems = mutableListOf<BankStatementItem?>()

        customerAccountCredits.filter {
            it.details.reason == CustomerAccountCreditReason.TRANSACTION_ROLLBACK
        }.forEach {
            undiscloseableItems.add(discloseableItems.removeLockBalanceDebit(it))
            undiscloseableItems.add(discloseableItems.removeUnlockBalanceCredit(it))
        }

        return discloseableItems to undiscloseableItems.filterNotNull()
    }

    private fun MutableList<BankStatementItem>.removeLockBalanceDebit(customerAccountCredit: CustomerAccountCredit) = firstOrNull { item ->
        item.flow == BankStatementItemFlow.DEBIT && item.amount == customerAccountCredit.amount && item.lastUpdate.inFiveMinutesRangeOf(customerAccountCredit.createdAt)
    }.also { matchingItem ->
        remove(matchingItem)
    }

    private fun MutableList<BankStatementItem>.removeUnlockBalanceCredit(customerAccountCredit: CustomerAccountCredit) = firstOrNull { item ->
        item.flow == BankStatementItemFlow.CREDIT && item.amount == customerAccountCredit.amount && item.lastUpdate.inFiveMinutesRangeOf(customerAccountCredit.createdAt)
    }.also { matchingItem ->
        remove(matchingItem)
    }

    private fun BillView.toLogAttribute() = mapOf(
        "amount" to amountPaid,
        "amountPaidWithBalance" to amountPaidWithBalance(),
        "amountPaidWithCreditCard" to amountPaidWithCreditCard(),
        "billId" to billId.value,
        "paidDate" to paidDate?.format(dateTimeFormat),
        "type" to billType,
    )

    private fun BankStatementItem.toLogAttribute() = mapOf(
        "amount" to amount,
        "date" to date.format(dateFormat),
        "description" to description,
        "operationNumber" to operationNumber,
        "ref" to ref,
        "lastUpdate" to lastUpdate?.format(dateTimeFormat),
        "documentNumber" to documentNumber,
        "counterpartAccountNo" to counterpartAccountNo,
        "flow" to flow,
    )

    private fun StatementItem.toLogAttribute() = mapOf(
        "id" to id,
        "amount" to amount,
        "counterPartName" to counterPartName,
        "postedAt" to postedAt.format(dateTimeFormat),
        "description" to description,
        "flow" to flow,
        "balance" to balance.amount,
        "category" to category,
    )

    private fun CustomerAccountCredit.toLogAttribute() = mapOf(
        "amount" to amount,
        "fromInternalBankAccount" to fromInternalBankAccount,
        "createdAt" to createdAt.format(dateTimeFormat),
        "details" to details.toLogAttribute(),
    )

    private fun CustomerAccountCreditDetails.toLogAttribute() = when (this) {
        is BonusCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "description" to description,
        )
        is TransactionRollbackCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "transactionId" to transactionId.value,
        )
        is RefundedSubscriptionCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "billId" to billId.value,
        )
        is RefundedBillCustomerAccountCreditDetails -> mapOf(
            "reason" to reason,
            "billId" to billId.value,
            "transactionId" to transactionId.value,
            "amountPaidWithBalance" to amountPaidWithBalance,
            "amountPaidWithCreditCard" to amountPaidWithCreditCard,
            "type" to type,
            "originalPaidDate" to originalPaidDate.format(dateTimeFormat),
        )
    }

    private fun BankStatementItem.toItem(balance: Balance? = null): StatementItem {
        return StatementItem(
            id = operationNumber.toSha256(),
            amount = amount,
            counterPartName = counterpartName,
            postedAt = lastUpdate ?: date.atStartOfDay().atZone(brazilTimeZone),
            description = description,
            flow = flow,
            balance = balance ?: Balance(0),
            category = null,
        )
    }

    private fun String.toSha256(): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(this.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }

    private fun BankStatement.ignoreFridayCreditMovements(accountId: AccountId): BankStatement {
        val (_, regularItems) = this.items.partitionByAbatementItems(accountId)

        return BankStatement(
            items = regularItems,
            initialBalance = this.initialBalance,
            finalBalance = Balance(this.finalBalance.amount),
        )
    }

    private fun List<BankStatementItem>.partitionByAbatementItems(accountId: AccountId): Pair<List<BankStatementItem>, List<BankStatementItem>> {
        val account = accountService.findAccountById(accountId)
        val isSelfAccount = account.document == selfCNPJArbiAccount

        // Note: pra quando é um reembolso. Esse crédito vem pela conta liquidação, mas não é um crédito de fato a ser contabilizado.
        // Mas se for um cashin feito por cartão, deve ser contabilizado
        val (abatementItems, regularItems) = this.partition {
            it.flow == BankStatementItemFlow.CREDIT && !isSelfAccount && it.counterpartDocument == selfCNPJArbiAccount && it.isSettlementAccount()
        }

        return Pair(abatementItems, regularItems)
    }

    private fun BankStatementItem.isSettlementAccount(): Boolean {
        return contaLiquidacao.contains(this.documentNumber) || (this.counterpartAccountNo?.let { contaLiquidacao.contains(it) } ?: false)
    }

    fun sendStatementByEmail(
        accountId: AccountId,
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate, // inclusive
    ): Either<StatementError, UserStatement> {
        try {
            val account = accountService.findAccountById(accountId)

            fun isFullMonth(startDate: LocalDate, endDate: LocalDate) =
                startDate.month == endDate.month && startDate.year == endDate.year && startDate.dayOfMonth == 1 && endDate.plusDays(
                    1,
                ).dayOfMonth == 1

            val fileName = if (isFullMonth(startDate, endDate)) {
                "${startDate.year}_${startDate.month.value}"
            } else {
                "de_${startDate.year}_${startDate.month.value}_${startDate.dayOfMonth}_ate_${endDate.year}_${endDate.month.value}_${endDate.dayOfMonth}"
            }

            val periodMessage = "${startDate.format(dateFormatBR)} a ${endDate.format(dateFormatBR)}"

            val wallet = walletService.findWalletOrNull(walletId) ?: return StatementError.WalletNotFound.left()
            val balance = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as InternalBankAccount
            val founderAccount = accountService.findAccountById(wallet.founder.accountId)

            val userStatement = findAllStatementsByDate(wallet, balance, startDate, endDate, false).getOrElse {
                return it.left()
            }

            val statement = Statement(
                name = founderAccount.name,
                walletName = walletService.findWallet(walletId).name,
                bankAccount = balance,
                startDate = startDate,
                endDate = endDate,
                created = getZonedDateTime().toLocalDateTime(),
                items = userStatement.statementItems,
                initialBalance = userStatement.initialBalance,
                finalBalance = userStatement.finalBalance,
                initialBalanceDate = startDate.minusDays(1),
                finalBalanceDate = endDate,
            )

            notificationAdapter.notifyAccountStatement(
                emailAddress = account.emailAddress,
                name = account.name,
                files = listOfNotNull(
                    ByteArrayWithNameAndType(
                        fileName = "Extrato_${displayName}_$fileName.csv",
                        mediaType = MediaType.TEXT_CSV,
                        data = statementItemConverter.convertToCsv(statement.items).toByteArray(),
                    ),
                    ByteArrayWithNameAndType(
                        fileName = "Extrato_${displayName}_$fileName.pdf",
                        mediaType = MediaType.APPLICATION_PDF,
                        data = statementItemConverter.convertToPdf(statement),
                    ),
                    if (account.isBetaGroup()) {
                        ByteArrayWithNameAndType(
                            fileName = "Extrato_${displayName}_$fileName.ofx",
                            mediaType = "application/ofx",
                            data = statementItemConverter.convertToOfx(statement),
                        )
                    } else {
                        null
                    },
                ),
                periodMessage = periodMessage,
            )
            return userStatement.right()
        } catch (e: ForwardMessageException) {
            return StatementError.FailedToSendEmail.left()
        } catch (e: Exception) {
            logger.error("StatementService#sendStatementByEmail", e)
            return StatementError.ServerError.left()
        }
    }

    fun requestStatement(
        accountId: AccountId,
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate, // inclusive
    ): Either<StatementError, Unit> {
        try {
            val wallet = walletService.findWallet(walletId)
            val walletMember = wallet.getActiveMember(accountId)

            if (walletMember.permissions.viewBills != BillPermission.ALL_BILLS) {
                return StatementError.NotAllowed.left()
            }

            sqsMessagePublisher.sendMessage(
                queueName = queueName,
                body = RequestStatementMessageTO(
                    accountId = accountId.value,
                    walletId = wallet.id.value,
                    startDate = startDate.format(DateTimeFormatter.ISO_DATE),
                    endDate = endDate.format(DateTimeFormatter.ISO_DATE),
                ),
            )

            return Unit.right()
        } catch (e: NoSuchElementException) {
            return StatementError.NotAllowed.left()
        } catch (e: Exception) {
            logger.error("StatementService#requestStatement", e)
            return StatementError.ServerError.left()
        }
    }

    fun getWalletPaymentMethodCreation(walletId: WalletId): Either<StatementError, LocalDate> {
        return try {
            val wallet = walletService.findWallet(walletId)
            val accountPaymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
                accountId = wallet.founder.accountId,
                accountPaymentMethodId = wallet.paymentMethodId,
            )
            accountPaymentMethod.created!!.toLocalDate().right()
        } catch (e: Exception) {
            StatementError.ServerError.left()
        }
    }

    fun resolveDuplicatedStatements(
        accountPaymentMethodId: AccountPaymentMethodId,
        dryRun: Boolean,
    ): List<String> {
        val logName = "StatementService#resolveDuplicatedStatements"

        val markers = Markers.append("accountPaymentMethodId", accountPaymentMethodId.value)
            .andAppend("dryRun", dryRun)

        val statement = internalBankRepository.findAllBankStatementItem(
            accountPaymentMethodId = accountPaymentMethodId,
            startDate = LocalDate.of(2025, Month.SEPTEMBER, 5),
            endDate = LocalDate.now(),
        )

        val duplicatedItems = statement.items.filter { it.endToEnd != null }.groupingBy { it.endToEnd!!.value }.eachCount()
            .filter { (_, count) -> count >= 2 }

        val removedOperationNumbers = mutableListOf<String>()
        duplicatedItems.forEach { duplicated ->
            markers.andAppend("endToEnd", duplicated.key)
            val list = statement.items.filter { it.endToEnd != null && it.endToEnd!!.value == duplicated.key }
            val (tempList, definitiveList) = list.partition { it.isTemporaryOperationNumber }
            markers.andAppend("temporaryItemsList", tempList)
                .andAppend("definitiveItemsList", definitiveList)

            if (tempList.size != 1 || definitiveList.size != 1) {
                markers.andAppend("error", "Listas de tamanhos inesperados")
                logger.warn(markers, logName)
                return emptyList()
            }
            val temp = tempList.first()
            markers.andAppend("temporaryOperationNumber", temp.operationNumber)
            removedOperationNumbers.add(temp.operationNumber)

            if (!dryRun) {
                internalBankRepository.remove(InternalBankStatementItem(temp, accountPaymentMethodId))
            }
        }
        logger.info(markers, logName)
        return removedOperationNumbers
    }

    private fun List<BillView>.removeOpenFinancePayments(): List<BillView> {
        return this.filter { it.source !is ActionSource.OpenFinance }
    }

    private fun List<BillView>.removeCreditCardPayments(): List<BillView> {
        return this.filter {
            when (it.paymentDetails) {
                is MultiplePaymentMethodsDetail -> {
                    it.paymentDetails.methods.find { paymentMethod ->
                        paymentMethod.methodType() == PaymentMethodType.CREDIT_CARD
                    } == null
                }

                is PaymentMethodsDetailWithBalance -> true
                is PaymentMethodsDetailWithCreditCard -> false
                is PaymentMethodsDetailWithExternalPayment -> false
                null -> false
            }
        }
    }

    private fun List<BillView>.filterPaidWithBalance(): List<BillView> {
        return this.filter { bill ->
            bill.source !is ActionSource.OpenFinance && when (bill.paymentDetails) {
                is MultiplePaymentMethodsDetail -> bill.paymentDetails.methods.any { it is PaymentMethodsDetailWithBalance }
                is PaymentMethodsDetailWithBalance -> true
                is PaymentMethodsDetailWithCreditCard -> false
                is PaymentMethodsDetailWithExternalPayment -> false
                null -> false
            }
        }
    }
    private fun List<CustomerAccountCredit>.filterPaidWithBalanceAndRefunded(): List<CustomerAccountCredit> {
        return this.filter {
            (it.details as RefundedBillCustomerAccountCreditDetails).amountPaidWithBalance > 0
        }
    }

    private fun MutableList<BillView>.findAndRemoveMatchingPayment(item: BankStatementItem): BillView? {
        return firstOrNull { paidBill ->
            item.amount == paidBill.paymentDetails?.amountPaidWithBalance() && item.lastUpdate.inFiveMinutesRangeOf(paidBill.paidDate?.atZone(brazilTimeZone))
        }.also {
            if (it != null) {
                remove(it)
            }
        }
    }

    private fun MutableList<CustomerAccountCredit>.findAndRemoveMatchingRefundedPayment(item: BankStatementItem): CustomerAccountCredit? {
        return firstOrNull { paidAndRefundedBill ->
            with(paidAndRefundedBill.details as RefundedBillCustomerAccountCreditDetails) {
                item.amount == amountPaidWithBalance && item.lastUpdate.inFiveMinutesRangeOf(originalPaidDate)
            }
        }.also {
            if (it != null) {
                remove(it)
            }
        }
    }

    private fun MutableList<CustomerAccountCredit>.findAndRemoveMatchingCredit(item: BankStatementItem): CustomerAccountCredit? {
        return firstOrNull { credit ->
            item.amount == credit.amount && item.lastUpdate.inFiveMinutesRangeOf(credit.createdAt)
        }.also {
            if (it != null) {
                remove(it)
            }
        }
    }

    private fun BillView.toItem(categories: Map<PFMCategoryId, String>): StatementItem {
        return StatementItem(
            id = billId.value.toSha256(),
            amount = paymentDetails?.amountPaidWithBalance() ?: 0,
            counterPartName = payee,
            postedAt = paidDate!!.atZone(brazilTimeZone),
            description = billDescription,
            flow = BankStatementItemFlow.DEBIT,
            balance = Balance(0),
            category = categoryId?.let { categories.getOrDefault(it, null) },
        )
    }

    private fun CustomerAccountCredit.toDebitItem(): StatementItem {
        val refundDetails = (details as RefundedBillCustomerAccountCreditDetails)
        return StatementItem(
            id = "${accountPaymentMethodId.value}#${createdAt.format(dateTimeFormat)}".toSha256(),
            amount = refundDetails.amountPaidWithBalance,
            counterPartName = refundDetails.billPayee,
            postedAt = refundDetails.originalPaidDate,
            description = "${refundDetails.billDescription} - estornado em ${createdAt.format(dateFormatBR)}".removePrefix(" - "),
            flow = BankStatementItemFlow.DEBIT,
            balance = Balance(0),
            category = null,
        )
    }

    private fun CustomerAccountCredit.toCreditItem(counterPartName: String): StatementItem {
        return StatementItem(
            id = "${accountPaymentMethodId.value}#${createdAt.format(dateTimeFormat)}".toSha256(),
            amount = amount,
            counterPartName = counterPartName,
            postedAt = createdAt,
            description = details.reason.description,
            flow = BankStatementItemFlow.CREDIT,
            balance = Balance(0),
            category = null,
        )
    }

    private fun ZonedDateTime?.inFiveMinutesRangeOf(zonedDateTime: ZonedDateTime?): Boolean {
        if (this == null || zonedDateTime == null) return false

        val rangeStart = zonedDateTime.minusMinutes(5)
        val rangeEnd = zonedDateTime.plusMinutes(5)
        return rangeStart < this && this < rangeEnd
    }
}

sealed class StatementError : Exception() {
    data object WalletNotFound : StatementError()
    data object AccountMissingPermissionError : StatementError()
    data object ServerError : StatementError()
    data object NotAllowed : StatementError()
    data object FailedToSendEmail : StatementError()
}

data class UserStatement(
    val walletId: WalletId,
    val startDate: LocalDate,
    val initialBalance: Balance,
    val statementItems: List<StatementItem>,
    val endDate: LocalDate,
    val finalBalance: Balance,
)

data class StatementItem(
    val id: String,
    val amount: Long,
    val counterPartName: String,
    val postedAt: ZonedDateTime,
    val description: String,
    val flow: BankStatementItemFlow,
    val balance: Balance,
    val category: String?,
)

data class RequestStatementMessageTO(
    val accountId: String,
    val walletId: String,
    val startDate: String,
    val endDate: String,
)