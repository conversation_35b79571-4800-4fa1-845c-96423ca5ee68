package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.text.DecimalFormat
import java.text.NumberFormat
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.math.roundToLong
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
class ArbiAccountStatementAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : AccountStatementAdapter {

    private val arbiDatePattern = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    override fun getStatement(
        accountNo: String,
        document: Document,
        initialDate: LocalDate,
        endDate: LocalDate,
        sortType: String,
        logBody: Boolean,
    ): BankStatement {
        val token = authenticationManager.getToken()
        val initialDateFormatted = initialDate.minusDays(4).format(arbiDatePattern)
        val endDateFormatted = endDate.plusDays(4).format(arbiDatePattern)
        val accountNumberFormatted = accountNo.padStart(10, '0')
        val requestId = UUID.randomUUID().toString()
        val marker = Markers.append("accountNo", accountNumberFormatted)
            .andAppend("initialDate", initialDateFormatted)
            .andAppend("endDate", endDateFormatted)
            .andAppend("requestId", requestId)
        val uri = UriBuilder.of(configuration.accountStatementPath)
            .queryParam("agencyCode", "00019")
            .queryParam("accountNumber", accountNumberFormatted)
            .queryParam("startDate", initialDate)
            .queryParam("finalDate", endDateFormatted)
            .queryParam("numberOfDays", "0")
            .queryParam("sortType", sortType)
            .queryParam("numberOfTheLastMovementSent", "0")
            .queryParam("quantityOfMovement", "0")
            .build()
        val httpRequest = HttpRequest.GET<String>(uri)
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .header("x-client-request-id", requestId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        marker.andAppend("uri", uri)
        val call = httpClient.exchange(
            httpRequest,
            Argument.listOf(ArbiBankAccountStatementItem::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            val body = response.body().sortedWith(
                compareBy<ArbiBankAccountStatementItem> { it.parsedInclusionDateTime }.thenBy { it.movementNumber },
            )
            logger.info(
                marker.andAppend("response", body),
                "ArbiAccountStatementAdapter",
            )
            return mapToBankStatement(initialDate, endDate, body, document)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(marker, e)

            marker.andAppend("response", e.response.body())
                .andAppend("status", e.status)

            val responseBody = e.response.getBody(String::class.java).get()

            val accountNotAllowed = e.status == HttpStatus.FORBIDDEN && responseBody
                .contains("TRANSACTION-PERMISSION-1")

            if (accountNotAllowed) {
                logger.warn(marker, "ArbiAccountStatementAdapter", e)
                throw ArbiAccountMissingPermissionsException()
            } else {
                logger.error(marker, "ArbiAccountStatementAdapter", e)
                throw ArbiBankAccountException()
            }
        }
    }

    private fun mapToBankStatement(
        initialDate: LocalDate,
        endDate: LocalDate,
        items: List<ArbiBankAccountStatementItem>,
        document: Document,
    ): BankStatement {
        if (items.size == 1 && items.first().releaseDate == null) {
            return BankStatement(
                items = emptyList(),
                initialBalance = Balance(convertToLong(items.first().previousBalance.toString())),
                finalBalance = Balance(convertToLong(items.first().previousBalance.toString())),
            )
        }
        val filteredItems = items.filter {
            it.parsedInclusionDate.isAfter(initialDate.minusDays(1)) && it.parsedInclusionDate.isBefore(endDate.plusDays(1))
        }

        val mappedItems = filteredItems.map {
            val counterpartDocument = it.counterpartyCpfCnpj?.padStart(11, '0').orEmpty()
            DefaultBankStatementItem(
                date = it.parsedInclusionDate,
                flow = if (it.transactionType == "C") BankStatementItemFlow.CREDIT else BankStatementItemFlow.DEBIT,
                type = getBankStatementType(it.historicalCode, counterpartDocument == document.value),
                description = it.historyDescription,
                operationNumber = it.movementNumber.toString(),
                amount = convertToLong(it.transactionValue.toString()),
                counterpartName = it.counterpartyName.orEmpty(),
                counterpartDocument = counterpartDocument.trim(),
                counterpartAccountNo = it.counterpartyAccount,
                documentNumber = document.value,
                lastUpdate = ZonedDateTime.of(
                    it.parsedInclusionDateTime,
                    brazilTimeZone,
                ),
            )
        }
        return if (filteredItems.isNotEmpty()) {
            BankStatement(
                items = mappedItems,
                initialBalance = Balance(convertToLong(filteredItems.first().previousBalanceMovement.toString())),
                finalBalance = Balance(convertToLong(filteredItems.last().updatedBalanceMovement.toString())),
            )
        } else {
            val lastItem = items.lastOrNull {
                it.parsedInclusionDate.isBefore(initialDate)
            }
            if (lastItem != null) {
                BankStatement(
                    items = mappedItems,
                    initialBalance = Balance(convertToLong(lastItem.updatedBalanceMovement.toString())),
                    finalBalance = Balance(convertToLong(lastItem.updatedBalanceMovement.toString())),
                )
            } else {
                val firstItem = items.first {
                    it.parsedInclusionDate.isAfter(endDate)
                }
                BankStatement(
                    items = mappedItems,
                    initialBalance = Balance(convertToLong(firstItem.previousBalanceMovement.toString())),
                    finalBalance = Balance(convertToLong(firstItem.previousBalanceMovement.toString())),
                )
            }
        }
    }

    private fun checkUnauthorized(markers: LogstashMarker, e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(markers.andAppend("error_message", "Token is expired"), "ArbiAccountStatementAdapter")
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    private fun getBankStatementType(code: String, sameOwnership: Boolean): BankStatementItemType {
        return when (code) {
            "00176" -> BankStatementItemType.TED_DIF_TITULARIDADE
            "00055" -> if (sameOwnership) BankStatementItemType.TED_MESMA_TITULARIDADE else BankStatementItemType.TED_DIF_TITULARIDADE
            "00156", "00057" -> BankStatementItemType.TED_MESMA_TITULARIDADE
            "00058" -> BankStatementItemType.DEVOLUCAO_TED
            "00102", "00259", "00213", "00011" -> BankStatementItemType.TRANSFERENCIA_CC
            "00264", "00265", "00266", "00267", "00319" -> BankStatementItemType.PIX
            "00268", "00269" -> BankStatementItemType.DEVOLUCAO_PIX
            "00558", "00632" -> BankStatementItemType.INVESTMENT_REDEMPTION
            "00029", "00197" -> BankStatementItemType.PAGAMENTO_BOLETO
            else -> BankStatementItemType.OUTROS
        }
    }

    private fun convertToLong(value: String): Long {
        if (value.isBlank()) {
            throw ArbiAdapterException()
        }

        val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
        nf.applyPattern("#.##")
        return (nf.parse(value).toDouble() * 100.0).roundToLong()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiAccountStatementAdapter::class.java)
    }
}

data class ArbiBankAccountStatementItem(
    val counterpartyAgency: String?,
    val counterpartyBank: String?,
    val counterpartyAccount: String?,
    val counterpartyCpfCnpj: String?,
    val counterpartyName: String?,
    val name: String,
    val agencyName: String,
    val documentNumber: String,
    val movementNumber: Long,
    val cpmfProvision: Double,
    val category: String,
    val alineaCode: String?,
    val historicalCode: String,
    val historyComplement: String?,
    val inclusionDate: String,
    val releaseDate: String?,
    val historyDescription: String,
    val reversed: String?,
    val limit: Double,
    val transactionType: String,
    val previousBalance: Double,
    val previousBalanceMovement: Double,
    val balanceAfterUltimatelyMovementDay: String?,
    val updatedBalanceMovement: Double,
    val blockedBalance: Double,
    val transactionValue: Double,
    val purchaseAmount: Double,
    val updateBalance: Double,
    val blockedAmount: Double,
    val balanceAvailable: Double,
    val cashAmount: Double,
    val errorCode: Long,
    val errorMessage: String?,
) {
    val parsedInclusionDateTime = if (inclusionDate.isBlank()) { LocalDateTime.MIN } else { LocalDateTime.parse(inclusionDate, arbiInclusionDateTimePattern) }
    val parsedInclusionDate = parsedInclusionDateTime.toLocalDate()

    companion object {
        private val arbiInclusionDateTimePattern = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss") // 03/10/2023 11:28:21
    }
}